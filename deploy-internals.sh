#!/bin/bash

APP_NAME=theinfini_ai_internals
NEW_TAG=$1  # e.g., 1.0.1
PORT_BASE=5499

if [ -z "$NEW_TAG" ]; then
  echo "❌ Usage: ./deploy-internals.sh <new-tag>"
  exit 1
fi

GREEN_PORT=$((PORT_BASE + 1))

echo "🚀 Deploying new internals admin as GREEN container..."

echo "APP NAME: ${APP_NAME}_green"
echo "NEW TAG: ${NEW_TAG}"
echo "GREEN PORT: ${GREEN_PORT}"

# Run green container
docker run -d --name ${APP_NAME}_green \
  --env-file .env \
  --add-host=host.docker.internal:host-gateway \
  -p ${GREEN_PORT}:5500 \
  --restart=always \
  theinfini_ai_internals:${NEW_TAG}

echo "⏳ Waiting for health check..."
sleep 20

HEALTH=$(curl -s http://localhost:${GREEN_PORT}/health)

if echo "$HEALTH" | grep -q '"status":"healthy"'; then
  echo "✅ Health check passed for GREEN"

  echo "🔁 Switching traffic to GREEN..."

  # Stop & remove blue if exists
  docker rm -f ${APP_NAME}_blue 2>/dev/null

  # Rename old container to blue
  docker rename ${APP_NAME} ${APP_NAME}_blue 2>/dev/null

  # Rename green to primary name
  docker rename ${APP_NAME}_green ${APP_NAME}

  echo "♻️ Cleanup: Removing old BLUE container..."
  docker rm -f ${APP_NAME}_blue

  echo "✅ Deployment complete. Now serving from ${APP_NAME} (${NEW_TAG})"
else
  echo "❌ Health check failed for GREEN"
  echo logs ${APP_NAME}_green
  exit 1
fi
