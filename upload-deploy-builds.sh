#!/bin/bash

echo ""
echo "=========================================="
echo "🚀 The Infini AI Internals | Docker Image Copy Utility"
echo "=========================================="
echo "This script performs the following steps:"
echo "1️⃣  Saves the specified Docker image as a .tar file"
echo "2️⃣  Transfers the tar file to the VPS"
echo "------------------------------------------"
echo ""

# Prompt for inputs
read -p "🖼️  Enter Docker image name (e.g. theinfini_ai_internals:latest): " IMAGE_NAME
read -p "📦  Enter tar file name (e.g. internals_image.tar): " TAR_NAME

# VPS configuration
VPS_USER="inf_admin"
VPS_IP="***************"  # Replace this
VPS_DEST_PATH="/home/<USER>/theinfini_ai/theinfini_ai_internals"

# Step 1: Remove existing tar if it exists
if [ -f "$TAR_NAME" ]; then
  echo "🗑️  Found existing file '$TAR_NAME'. Removing it..."
  rm "$TAR_NAME"
fi

# Step 2: Save Docker image
echo "📦 Saving Docker image '$IMAGE_NAME' to '$TAR_NAME'..."
docker save -o "$TAR_NAME" "$IMAGE_NAME"

if [ $? -ne 0 ]; then
  echo "❌ Failed to save Docker image. Check if the image name is correct."
  exit 1
fi

# Show absolute path of tar file
ABS_PATH="$(realpath "$TAR_NAME")"
echo "📁 Docker image saved at: $ABS_PATH"

# Step 3: Copy tar to VPS
echo "🚚 Transferring '$TAR_NAME' to $VPS_USER@$VPS_IP:$VPS_DEST_PATH ..."
scp "$TAR_NAME" "$VPS_USER@$VPS_IP:$VPS_DEST_PATH"

if [ $? -ne 0 ]; then
  echo "❌ Transfer failed. Ensure SSH access and IP address are correct."
  exit 1
fi

echo ""
echo "✅ Image successfully copied to VPS."
echo "👉 To load the image on the VPS, run:"
echo "    docker load -i $VPS_DEST_PATH/$TAR_NAME"
echo ""
