const jwt = require('jsonwebtoken');
const { logger } = require('../config/logger');
const environment = require('../config/environment');
const adminService = require('../services/adminService');
const { getUserDb } = require('../config/db');

/**
 * JWT Utility Class
 * Handles JWT token generation and verification using same secret as main app
 */
class JWTUtil {
  static SECRET = environment.JWT_SECRET;
  static EXPIRES_IN = environment.JWT_EXPIRES_IN;

  /**
   * Generate a JWT token
   * @param {Object} payload - The payload to encode
   * @returns {string} The generated JWT token
   */
  static generateToken(payload) {
    try {
      return jwt.sign(payload, this.SECRET, {
        expiresIn: this.EXPIRES_IN,
      });
    } catch (error) {
      logger.error('Error generating JWT token:', error);
      throw new Error('Failed to generate token');
    }
  }

  /**
   * Verify and decode a JWT token
   * @param {string} token - The JWT token to verify
   * @returns {Object} The decoded payload
   * @throws {Error} If token is invalid or expired
   */
  static verifyToken(token) {
    try {
      return jwt.verify(token, this.SECRET);
    } catch (error) {
      logger.error('Error verifying JWT token:', error);
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Decode a JWT token without verification (for debugging)
   * @param {string} token - The JWT token to decode
   * @returns {Object} The decoded payload
   */
  static decodeToken(token) {
    return jwt.decode(token);
  }

  /**
   * Check if a token is expired
   * @param {string} token - The JWT token to check
   * @returns {boolean} True if token is expired
   */
  static isTokenExpired(token) {
    try {
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) {
        return true;
      }
      return Date.now() >= decoded.exp * 1000;
    } catch (error) {
      return true;
    }
  }
}

/**
 * Middleware to authenticate JWT token from session or header
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const authenticateToken = async (req, res, next) => {
  try {
    // Try to get token from session first, then from Authorization header
    let token = req.session?.authToken;

    if (!token) {
      const authHeader = req.headers.authorization;
      token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    }

    if (!token) {
      logger.warn('Authentication failed: No token provided');
      return res.redirect('/admin/auth/login');
    }

    // Verify token
    const payload = JWTUtil.verifyToken(token);
    req.user = payload;

    logger.debug(`User authenticated: ${payload.userId} (${payload.email})`);
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    req.session.destroy((err) => {
      if (err) {
        logger.error('Error destroying session:', err);
      }
      res.redirect('/admin/auth/login');
    });
  }
};

/**
 * Middleware to check if user has SUPER_ADMIN role
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requireSuperAdmin = async (req, res, next) => {
  try {
    if (!req.user || !req.user.userId) {
      logger.warn('Access denied: User not authenticated');
      // Clear session and redirect to login
      req.session.destroy((err) => {
        if (err) {
          logger.error('Error destroying session:', err);
        }
        res.redirect('/admin/auth/login?message=login_required');
      });
      return;
    }

    const db = getUserDb();

    // Check if user has SUPER_ADMIN role
    const [userRoles] = await db.execute(`
      SELECT r.name
      FROM user_roles ur
      INNER JOIN roles r ON ur.role_id = r.id
      WHERE ur.user_id = ? AND ur.is_active = 1 AND r.is_active = 1 AND r.name = 'SUPER_ADMIN'
    `, [req.user.userId]);

    if (userRoles.length === 0) {
      logger.warn(`Access denied: User ${req.user.userId} (${req.user.email}) does not have SUPER_ADMIN role`);
      // Clear session and redirect to login with access denied message
      req.session.destroy((err) => {
        if (err) {
          logger.error('Error destroying session:', err);
        }
        res.redirect('/admin/auth/login?message=access_denied');
      });
      return;
    }

    logger.debug(`Super Admin access granted: ${req.user.userId} (${req.user.email})`);
    next();
  } catch (error) {
    logger.error('Error checking super admin role:', error);
    // Clear session and redirect to login on error
    req.session.destroy((err) => {
      if (err) {
        logger.error('Error destroying session:', err);
      }
      res.redirect('/admin/auth/login?message=error');
    });
  }
};

/**
 * Middleware to check if user is already authenticated (for login page)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const redirectIfAuthenticated = async (req, res, next) => {
  try {
    const token = req.session?.authToken;
    
    if (token) {
      try {
        JWTUtil.verifyToken(token);
        logger.debug('User already authenticated, redirecting to dashboard');
        return res.redirect('/admin/dashboard');
      } catch (error) {
        // Token is invalid, clear session and continue to login
        req.session.destroy((err) => {
          if (err) {
            logger.error('Error destroying session:', err);
          }
        });
      }
    }
    
    next();
  } catch (error) {
    logger.error('Error in redirectIfAuthenticated middleware:', error);
    next();
  }
};

/**
 * Middleware to extract client IP address
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const extractClientIP = (req, res, next) => {
  try {
    // Get client IP from various possible headers
    const clientIP = req.headers['x-forwarded-for'] ||
                    req.headers['x-real-ip'] ||
                    req.connection?.remoteAddress ||
                    req.socket?.remoteAddress ||
                    req.ip ||
                    '127.0.0.1';

    req.clientIP = Array.isArray(clientIP) ? clientIP[0] : clientIP.split(',')[0].trim();
    
    logger.debug(`Client IP extracted: ${req.clientIP}`);
    next();
  } catch (error) {
    logger.error('Error extracting client IP:', error);
    req.clientIP = '127.0.0.1';
    next();
  }
};

/**
 * Rate limiting middleware per IP
 * @param {number} maxRequests - Maximum number of requests
 * @param {number} windowMs - Time window in milliseconds
 * @returns {Function} Middleware function
 */
const rateLimitPerIP = (maxRequests, windowMs) => {
  const ipRequests = new Map();

  return (req, res, next) => {
    try {
      const clientIP = req.clientIP || req.ip || 'unknown';
      const now = Date.now();

      const ipLimit = ipRequests.get(clientIP);
      
      if (!ipLimit || now > ipLimit.resetTime) {
        // Reset or initialize limit
        ipRequests.set(clientIP, {
          count: 1,
          resetTime: now + windowMs,
        });
        next();
        return;
      }

      if (ipLimit.count >= maxRequests) {
        logger.warn(`Rate limit exceeded for IP: ${clientIP}`);
        return res.status(429).render('error', {
          title: 'Rate Limit Exceeded',
          message: 'Too many requests. Please try again later.',
          error: { status: 429 }
        });
      }

      ipLimit.count++;
      next();
    } catch (error) {
      logger.error('Error in rate limiting:', error);
      next();
    }
  };
};

/**
 * Middleware to make user data available to all views
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const setUserLocals = async (req, res, next) => {
  try {
    // Check if user is already set by authentication middleware
    if (req.user) {
      res.locals.user = req.user;
      res.locals.isAuthenticated = true;
      logger.debug(`User locals set from req.user: ${req.user.userId}`);
    } else {
      // Check session for authentication token
      const token = req.session?.authToken;
      const sessionUser = req.session?.user;

      if (token && sessionUser) {
        try {
          // Verify token is still valid
          const payload = JWTUtil.verifyToken(token);

          // Set user data from session
          res.locals.user = sessionUser;
          res.locals.isAuthenticated = true;

          logger.debug(`User locals set from session: ${sessionUser.id} (${sessionUser.email})`);
        } catch (error) {
          // Token is invalid, clear session
          logger.warn('Invalid token in session, clearing session');
          req.session.destroy((err) => {
            if (err) {
              logger.error('Error destroying session:', err);
            }
          });

          res.locals.user = null;
          res.locals.isAuthenticated = false;
        }
      } else {
        res.locals.user = null;
        res.locals.isAuthenticated = false;
      }
    }

    // Get open tickets count for sidebar (for all pages)
    try {
      res.locals.openTicketsCount = await adminService.getOpenTicketsCount();
    } catch (error) {
      logger.error('Error fetching open tickets count for sidebar:', error);
      res.locals.openTicketsCount = 0;
    }

    next();
  } catch (error) {
    logger.error('Error setting user locals:', error);
    res.locals.user = null;
    res.locals.isAuthenticated = false;
    res.locals.openTicketsCount = 0;
    next();
  }
};

module.exports = {
  JWTUtil,
  authenticateToken,
  requireSuperAdmin,
  redirectIfAuthenticated,
  extractClientIP,
  rateLimitPerIP,
  setUserLocals
};
