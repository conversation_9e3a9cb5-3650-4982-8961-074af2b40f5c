<%-include("../partials/header")%>

<!-- Paid Users Content -->
<div class="dashboard">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="page-header-content">
            <div>
                <h2>Paid Users</h2>
                <p>Users with active, cancelled, or expired subscriptions</p>
            </div>
            <div class="page-actions">
                <a href="/admin/subscriptions" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Subscriptions
                </a>
            </div>
        </div>
    </div>

    <!-- Users Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-users"></i>
                Paid Users (<%= pagination.totalUsers %> total)
            </h3>
            <div class="section-actions">
                <div class="search-container">
                    <form method="GET" class="search-form">
                        <div class="search-input-group">
                            <input type="text"
                                   name="search"
                                   placeholder="Search by name or email..."
                                   value="<%= typeof search !== 'undefined' ? search : '' %>"
                                   class="search-input">
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                            <% if (typeof search !== 'undefined' && search) { %>
                                <a href="/admin/subscriptions/paid-users" class="clear-search-btn" title="Clear search">
                                    <i class="fas fa-times"></i>
                                </a>
                            <% } %>
                        </div>
                        <input type="hidden" name="page" value="1">
                        <input type="hidden" name="limit" value="<%= pagination.limit %>">
                    </form>
                </div>
                <div class="pagination-info">
                    Page <%= pagination.currentPage %> of <%= pagination.totalPages %>
                    <% if (typeof search !== 'undefined' && search) { %>
                        <small class="search-results">Filtered by: "<%= search %>"</small>
                    <% } %>
                </div>
            </div>
        </div>
        <div class="section-content">
            <% if (users && users.length > 0) { %>
                <div class="users-table-container">
                    <table class="users-table">
                        <thead>
                            <tr>
                                <th>User Details</th>
                                <th>Subscription</th>
                                <th>Plan Details</th>
                                <th>Status</th>
                                <th>Dates</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% users.forEach(user => { %>
                                <tr>
                                    <td>
                                        <div class="user-info">
                                            <div class="user-avatar">
                                                <% if (user.profile_picture) { %>
                                                    <img src="<%= user.profile_picture %>" alt="Profile">
                                                <% } else { %>
                                                    <div class="avatar-placeholder">
                                                        <%= (user.first_name || user.email.charAt(0)).toUpperCase() %>
                                                    </div>
                                                <% } %>
                                            </div>
                                            <div class="user-details">
                                                <div class="user-name">
                                                    <% if (user.first_name || user.last_name) { %>
                                                        <%= (user.first_name || '') + ' ' + (user.last_name || '') %>
                                                    <% } else { %>
                                                        <span class="no-name">No name provided</span>
                                                    <% } %>
                                                </div>
                                                <div class="user-email"><%= user.email %></div>
                                                <% if (user.mobile) { %>
                                                    <div class="user-mobile"><%= user.mobile %></div>
                                                <% } %>
                                                <div class="user-badges">
                                                    <span class="badge <%= user.user_active ? 'active' : 'inactive' %>">
                                                        <%= user.user_active ? 'Active' : 'Inactive' %>
                                                    </span>
                                                    <span class="badge <%= user.is_verified ? 'verified' : 'unverified' %>">
                                                        <%= user.is_verified ? 'Verified' : 'Unverified' %>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="subscription-info">
                                            <div class="subscription-id">
                                                <small>ID: <%= user.subscription_id.substring(0, 8) %>...</small>
                                            </div>
                                            <div class="subscription-status">
                                                <span class="status-badge <%= user.subscription_status.toLowerCase() %>">
                                                    <i class="fas fa-circle"></i>
                                                    <%= user.subscription_status %>
                                                </span>
                                            </div>
                                            <% if (user.cancellation_reason) { %>
                                                <div class="cancellation-reason">
                                                    <small><strong>Reason:</strong> <%= user.cancellation_reason %></small>
                                                </div>
                                            <% } %>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="plan-info">
                                            <div class="plan-name"><%= user.plan_name %></div>
                                            <div class="plan-type">
                                                <span class="plan-badge <%= user.plan_type.toLowerCase() %>">
                                                    <%= user.plan_type %>
                                                </span>
                                            </div>
                                            <div class="plan-price">
                                                <%= user.currency %> <%= user.price %> / <%= user.billing_cycle.toLowerCase() %>
                                            </div>
                                            <div class="plan-credits">
                                                <% if (user.is_unlimited_credits) { %>
                                                    <i class="fas fa-infinity"></i> Unlimited
                                                <% } else { %>
                                                    <i class="fas fa-coins"></i> <%= user.credits %>
                                                <% } %>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="dates-info">
                                            <div class="date-item">
                                                <small><strong>Started:</strong></small>
                                                <div><%= new Date(user.start_date).toLocaleDateString() %></div>
                                            </div>
                                            <% if (user.end_date) { %>
                                                <div class="date-item">
                                                    <small><strong>Ends:</strong></small>
                                                    <div><%= new Date(user.end_date).toLocaleDateString() %></div>
                                                </div>
                                            <% } %>
                                            <% if (user.next_billing_date) { %>
                                                <div class="date-item">
                                                    <small><strong>Next Billing:</strong></small>
                                                    <div><%= new Date(user.next_billing_date).toLocaleDateString() %></div>
                                                </div>
                                            <% } %>
                                            <% if (user.cancelled_at) { %>
                                                <div class="date-item">
                                                    <small><strong>Cancelled:</strong></small>
                                                    <div><%= new Date(user.cancelled_at).toLocaleDateString() %></div>
                                                </div>
                                            <% } %>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="user-joined">
                                            <small><strong>Joined:</strong></small>
                                            <div><%= new Date(user.user_created_at).toLocaleDateString() %></div>
                                        </div>
                                    </td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="pagination-container">
                    <div class="pagination">
                        <%
                        const searchParam = typeof search !== 'undefined' && search ? '&search=' + encodeURIComponent(search) : '';
                        %>
                        <% if (pagination.hasPrev) { %>
                            <a href="?page=<%= pagination.currentPage - 1 %>&limit=<%= pagination.limit %><%= searchParam %>" class="pagination-btn">
                                <i class="fas fa-chevron-left"></i>
                                Previous
                            </a>
                        <% } %>

                        <div class="pagination-info">
                            <span>Page <%= pagination.currentPage %> of <%= pagination.totalPages %></span>
                            <span class="total-count">(<%= pagination.totalUsers %> total users)</span>
                        </div>

                        <% if (pagination.hasNext) { %>
                            <a href="?page=<%= pagination.currentPage + 1 %>&limit=<%= pagination.limit %><%= searchParam %>" class="pagination-btn">
                                Next
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <% } %>
                    </div>
                </div>
            <% } else { %>
                <div class="empty-state">
                    <i class="fas fa-users" style="font-size: 48px; color: var(--text-muted); margin-bottom: 16px;"></i>
                    <h4>No Paid Users Found</h4>
                    <p>There are no users with subscriptions at the moment.</p>
                </div>
            <% } %>
        </div>
    </div>
</div>

<%-include("../partials/footer")%>
