<%-include("../partials/header")%>

<!-- User Roles Content -->
<div class="dashboard">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <h2>User Roles Management</h2>
        <p>Manage user permissions and access levels</p>
    </div>

    <!-- User Roles Management Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-users-cog"></i>
                User Roles Management
            </h3>
        </div>
        <div class="section-content">
            <div class="subscription-actions">
                <div class="action-cards">
                    <div class="action-card">
                        <div class="action-count">
                            <div class="count-number" id="totalRolesCount">
                                <%= userRoles.roles ? userRoles.roles.length : '0' %>
                            </div>
                        </div>
                        <div class="count-label">Total Roles</div>
                        <div class="action-content">
                            <h4>Create New Role</h4>
                            <p>Create new roles with custom permissions and access levels for your organization.</p>
                            <button id="createRoleBtn" class="action-btn primary">
                                <i class="fas fa-plus"></i>
                                Create Role
                            </button>
                        </div>
                    </div>

                    <div class="action-card">
                        <div class="action-count">
                            <div class="count-number" id="assignedUsersCount">
                                0
                            </div>
                        </div>
                        <div class="count-label">Users with Roles</div>
                        <div class="action-content">
                            <h4>Assign Roles to Users</h4>
                            <p>Search for users by email and assign or modify their roles and permissions.</p>
                            <button id="assignRolesBtn" class="action-btn secondary">
                                <i class="fas fa-user-tag"></i>
                                Assign Roles
                            </button>
                        </div>
                    </div>

                    <div class="action-card">
                        <div class="action-count">
                            <div class="count-number" id="activeRolesCount">
                                <%= userRoles.roles ? userRoles.roles.filter(r => r.is_active).length : '0' %>
                            </div>
                        </div>
                        <div class="count-label">Active Roles</div>
                        <div class="action-content">
                            <h4>View All Roles</h4>
                            <p>View and manage all created roles, their descriptions, and assigned permissions.</p>
                            <button id="viewRolesBtn" class="action-btn tertiary">
                                <i class="fas fa-list"></i>
                                View Roles
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Role Modal -->
<div id="createRoleModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Create New Role</h3>
            <button class="modal-close" id="createRoleModalClose">&times;</button>
        </div>
        <div class="modal-body">
            <form id="createRoleForm" class="form-grid">
                <div class="form-group">
                    <label for="roleName">Role Name *</label>
                    <input type="text" id="roleName" name="roleName" required placeholder="Enter role name (e.g., 'Super Admin' will become 'SUPER_ADMIN')">
                    <small class="form-help">Role names will be automatically formatted to UPPERCASE with underscores</small>
                </div>
                <div class="form-group">
                    <label for="roleDescription">Description</label>
                    <textarea id="roleDescription" name="roleDescription" rows="3" placeholder="Enter role description"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Create Role
                    </button>
                    <button type="button" class="btn btn-secondary" id="createRoleCancelBtn">
                        <i class="fas fa-times"></i>
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Assign Roles Modal -->
<div id="assignRolesModal" class="modal" style="display: none;">
    <div class="modal-content large">
        <div class="modal-header">
            <h3>Assign Roles to User</h3>
            <button class="modal-close" id="assignRolesModalClose">&times;</button>
        </div>
        <div class="modal-body">
            <!-- User Search -->
            <div class="user-search-section">
                <div class="search-form">
                    <div class="form-group">
                        <label for="userEmail">Search User by Email *</label>
                        <div class="input-group">
                            <input type="email" id="userEmail" name="userEmail" required placeholder="Enter user email">
                            <button type="button" id="searchUserBtn" class="btn btn-secondary">
                                <i class="fas fa-search"></i>
                                Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Details (Hidden initially) -->
            <div id="userDetailsSection" class="user-details-section" style="display: none;">
                <div class="user-card">
                    <div class="user-info">
                        <div class="user-avatar">
                            <div class="avatar-placeholder" id="userAvatar">U</div>
                        </div>
                        <div class="user-details">
                            <h4 id="userName">User Name</h4>
                            <p id="userEmailDisplay"><EMAIL></p>
                            <div class="user-meta">
                                <span class="user-plan" id="userPlan">EXPLORER</span>
                                <span class="user-status" id="userStatus">Active</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Role Assignment -->
                <div class="role-assignment-section">
                    <h4>Available Roles</h4>
                    <div id="rolesContainer" class="roles-container">
                        <!-- Roles will be loaded here -->
                    </div>
                    <div class="form-actions">
                        <button type="button" id="assignRolesSubmitBtn" class="btn btn-primary" disabled>
                            <i class="fas fa-user-tag"></i>
                            Assign Role(s)
                        </button>
                        <button type="button" class="btn btn-secondary" id="resetUserSearchBtn">
                            <i class="fas fa-undo"></i>
                            Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- All Roles Modal -->
<div id="allRolesModal" class="modal" style="display: none;">
    <div class="modal-content large">
        <div class="modal-header">
            <h3>All Roles</h3>
            <button class="modal-close" id="allRolesModalClose">&times;</button>
        </div>
        <div class="modal-body">
            <div id="rolesOverview" class="roles-overview">
                <!-- Roles overview will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading...</p>
    </div>
</div>

<!-- User Roles JavaScript -->
<script src="/js/user-roles.js"></script>

<%-include("../partials/footer")%>
