<%-include("../partials/header")%>

<!-- Add Subscription Content -->
<div class="dashboard">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="page-header-content">
            <div>
                <h2>Add Subscription</h2>
                <p>Add a subscription to a user by searching with email and selecting a plan</p>
            </div>
            <div class="page-actions">
                <a href="/admin/subscriptions" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    Back to Subscriptions
                </a>
            </div>
        </div>
    </div>

    <!-- Add Subscription Form -->
    <div class="dashboard-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-plus-circle"></i>
                Add User Subscription
            </h3>
        </div>
        <div class="section-content">
            <!-- Step 1: User Search -->
            <div class="subscription-step" id="step-1">
                <div class="step-header">
                    <h4><span class="step-number">1</span> Search User</h4>
                    <p>Enter the user's email address to find their account</p>
                </div>
                <div class="step-content">
                    <div class="form-group">
                        <label for="userEmail">User Email Address</label>
                        <div class="input-group">
                            <input type="email" id="userEmail" placeholder="Enter user email address" required>
                            <button type="button" id="searchUserBtn" class="btn primary">
                                <i class="fas fa-search"></i>
                                Search User
                            </button>
                        </div>
                        <div id="userSearchError" class="error-message" style="display: none;"></div>
                    </div>
                    
                    <!-- User Details (shown after search) -->
                    <div id="userDetails" class="user-details" style="display: none;">
                        <div class="user-card">
                            <div class="user-info">
                                <div class="user-avatar">
                                    <img id="userAvatar" src="" alt="User Avatar" style="display: none;">
                                    <div id="userInitials" class="user-initials"></div>
                                </div>
                                <div class="user-data">
                                    <h5 id="userName"></h5>
                                    <p id="userEmailDisplay"></p>
                                    <div class="user-meta">
                                        <span id="userStatus" class="status-badge"></span>
                                        <span id="userPlan" class="plan-badge"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="user-subscription">
                                <div id="currentSubscription" style="display: none;">
                                    <h6>Current Subscription</h6>
                                    <p id="subscriptionDetails"></p>
                                </div>
                                <div id="noSubscription" style="display: none;">
                                    <h6>No Active Subscription</h6>
                                    <p>User is currently on the free plan</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Plan Selection -->
            <div class="subscription-step" id="step-2" style="display: none;">
                <div class="step-header">
                    <h4><span class="step-number">2</span> Select Plan</h4>
                    <p>Choose a subscription plan for the user</p>
                </div>
                <div class="step-content">
                    <div class="plans-grid">
                        <% if (plans && plans.length > 0) { %>
                            <% plans.forEach(plan => { %>
                                <div class="plan-card selectable" data-plan-id="<%= plan.id %>" data-plan-type="<%= plan.plan_type %>">
                                    <div class="plan-header">
                                        <div class="plan-type-badge <%= plan.plan_type.toLowerCase() %>">
                                            <%= plan.plan_type %>
                                        </div>
                                        <h5 class="plan-name"><%= plan.name %></h5>
                                        <div class="plan-price">
                                            <span class="currency"><%= plan.currency %></span>
                                            <span class="amount"><%= plan.price %></span>
                                            <span class="cycle">/ <%= plan.billing_cycle.toLowerCase() %></span>
                                        </div>
                                    </div>
                                    
                                    <div class="plan-body">
                                        <% if (plan.description) { %>
                                            <p class="plan-description"><%= plan.description %></p>
                                        <% } %>
                                        
                                        <div class="plan-credits">
                                            <% if (plan.is_unlimited_credits) { %>
                                                <span class="credits unlimited">
                                                    <i class="fas fa-infinity"></i>
                                                    Unlimited Credits
                                                </span>
                                            <% } else { %>
                                                <span class="credits limited">
                                                    <i class="fas fa-coins"></i>
                                                    <%= plan.credits %> Credits
                                                </span>
                                            <% } %>
                                        </div>
                                        
                                        <% if (plan.features) { %>
                                            <div class="plan-features">
                                                <h6>Features:</h6>
                                                <ul class="features-list">
                                                    <% 
                                                    try {
                                                        const features = JSON.parse(plan.features);
                                                        Object.entries(features).forEach(([key, value]) => {
                                                    %>
                                                        <li><strong><%= key %>:</strong> <%= value %></li>
                                                    <% 
                                                        });
                                                    } catch (e) {
                                                    %>
                                                        <li>Features available</li>
                                                    <% } %>
                                                </ul>
                                            </div>
                                        <% } %>
                                    </div>
                                    
                                    <div class="plan-footer">
                                        <div class="plan-select-indicator">
                                            <i class="fas fa-check-circle"></i>
                                            Selected
                                        </div>
                                    </div>
                                </div>
                            <% }); %>
                        <% } else { %>
                            <div class="no-plans">
                                <p>No active subscription plans available</p>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>

            <!-- Step 3: Review and Confirm -->
            <div class="subscription-step" id="step-3" style="display: none;">
                <div class="step-header">
                    <h4><span class="step-number">3</span> Review & Confirm</h4>
                    <p>Review the subscription details before adding</p>
                </div>
                <div class="step-content">
                    <div class="review-card">
                        <div class="review-section">
                            <h5>User Details</h5>
                            <div class="review-item">
                                <span class="label">Name:</span>
                                <span id="reviewUserName" class="value"></span>
                            </div>
                            <div class="review-item">
                                <span class="label">Email:</span>
                                <span id="reviewUserEmail" class="value"></span>
                            </div>
                        </div>
                        
                        <div class="review-section">
                            <h5>Subscription Plan</h5>
                            <div class="review-item">
                                <span class="label">Plan:</span>
                                <span id="reviewPlanName" class="value"></span>
                            </div>
                            <div class="review-item">
                                <span class="label">Type:</span>
                                <span id="reviewPlanType" class="value"></span>
                            </div>
                            <div class="review-item">
                                <span class="label">Price:</span>
                                <span id="reviewPlanPrice" class="value"></span>
                            </div>
                            <div class="review-item">
                                <span class="label">Billing:</span>
                                <span id="reviewBillingCycle" class="value"></span>
                            </div>
                            <div class="review-item">
                                <span class="label">Credits:</span>
                                <span id="reviewCredits" class="value"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button type="button" id="addSubscriptionBtn" class="btn success large">
                            <i class="fas fa-plus-circle"></i>
                            Add Subscription
                        </button>
                        <button type="button" id="cancelBtn" class="btn secondary large">
                            <i class="fas fa-times"></i>
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="successModal" class="modal" style="display: none;">
    <div class="modal-content success">
        <div class="modal-header">
            <h4><i class="fas fa-check-circle"></i> Subscription Added Successfully</h4>
        </div>
        <div class="modal-body">
            <p>The subscription has been successfully added to the user's account.</p>
            <div id="successDetails" class="success-details"></div>
        </div>
        <div class="modal-footer">
            <button type="button" id="viewSubscriptionsBtn" class="btn primary">
                <i class="fas fa-eye"></i>
                View Subscriptions
            </button>
            <button type="button" id="addAnotherBtn" class="btn secondary">
                <i class="fas fa-plus"></i>
                Add Another
            </button>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-spinner">
        <div class="spinner"></div>
        <p>Processing...</p>
    </div>
</div>

<script src="/js/add-subscription.js"></script>

<%-include("../partials/footer")%>
