<%-include("../partials/header")%>

<!-- Sales Reports Content -->
<div class="dashboard">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <h2>Sales Reports</h2>
        <p>View subscription analytics and sales performance</p>
    </div>

    <!-- Report Filters Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-filter"></i>
                Report Filters
            </h3>
        </div>
        <div class="section-content">
            <div class="filter-controls">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="monthSelect">Select Month:</label>
                        <input type="month" id="monthSelect" class="filter-input" value="<%= selectedMonth || new Date().toISOString().slice(0, 7) %>">
                    </div>

                    <div class="filter-separator">OR</div>

                    <div class="filter-group">
                        <label>Select Date Range (Max 6 months):</label>
                        <div class="date-range-group">
                            <input type="date" id="startDate" class="filter-input" placeholder="Start Date" value="<%= selectedStartDate %>">
                            <span class="date-separator">to</span>
                            <input type="date" id="endDate" class="filter-input" placeholder="End Date" value="<%= selectedEndDate %>">
                        </div>
                    </div>
                </div>

                <div class="filter-actions">
                    <button id="applyFilters" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        Apply Filters
                    </button>
                    <button id="clearFilters" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Metrics Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-chart-line"></i>
                Sales Metrics
                <span class="report-period">(<%= salesData.reportPeriod %>)</span>
            </h3>
            <div class="section-actions">
                <button id="refreshReports" class="btn btn-secondary btn-sm">
                    <i class="fas fa-refresh"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="section-content">
            <div class="subscription-actions">
                <div class="action-cards">
                    <div class="action-card">
                        <div class="action-count">
                            <div class="count-number" id="activeSubscriptionsCount">
                                <%= salesData.activeSubscriptions %>
                            </div>
                        </div>
                        <div class="count-label">Active Subscriptions</div>
                        <div class="action-content">
                            <h4>Active Subscriptions</h4>
                            <p id="activeSubscriptionsText">Number of active subscriptions in <span class="period-text"><%= salesData.reportPeriod %></span>.</p>
                            <div class="metric-info">
                                <i class="fas fa-check-circle text-success"></i>
                                <span>Currently Active</span>
                            </div>
                        </div>
                    </div>

                    <div class="action-card">
                        <div class="action-count">
                            <div class="count-number" id="addOnsCount">
                                <%= salesData.addOns %>
                            </div>
                        </div>
                        <div class="count-label">Add-ons</div>
                        <div class="action-content">
                            <h4>Add-ons Purchased</h4>
                            <p id="addOnsText">Number of add-ons purchased in <span class="period-text"><%= salesData.reportPeriod %></span>.</p>
                            <div class="metric-info">
                                <i class="fas fa-plus-circle text-info"></i>
                                <span>Additional Services</span>
                            </div>
                        </div>
                    </div>

                    <div class="action-card">
                        <div class="action-count">
                            <div class="count-number" id="cancelledSubscriptionsCount">
                                <%= salesData.cancelledSubscriptions %>
                            </div>
                        </div>
                        <div class="count-label">Cancelled Subscriptions</div>
                        <div class="action-content">
                            <h4>Cancelled Subscriptions</h4>
                            <p id="cancelledSubscriptionsText">Number of subscriptions cancelled in <span class="period-text"><%= salesData.reportPeriod %></span>.</p>
                            <div class="metric-info">
                                <i class="fas fa-times-circle text-danger"></i>
                                <span>Cancelled</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-dollar-sign"></i>
                Revenue
                <span class="report-period">(<%= salesData.reportPeriod %>)</span>
            </h3>
        </div>
        <div class="section-content">
            <div class="subscription-actions">
                <div class="action-cards">
                    <div class="action-card">
                        <div class="action-count">
                            <div class="count-number" id="totalRevenueCount">
                                <%
                                    const totalRevenue = salesData.revenue.total;
                                    let abbreviatedTotal = '';
                                    if (totalRevenue >= 10000000) {
                                        abbreviatedTotal = '₹' + (totalRevenue / 10000000).toFixed(1) + 'Cr';
                                    } else if (totalRevenue >= 100000) {
                                        abbreviatedTotal = '₹' + (totalRevenue / 100000).toFixed(1) + 'L';
                                    } else if (totalRevenue >= 1000) {
                                        abbreviatedTotal = '₹' + (totalRevenue / 1000).toFixed(1) + 'k';
                                    } else {
                                        abbreviatedTotal = '₹' + totalRevenue.toFixed(2);
                                    }
                                %>
                                <%= abbreviatedTotal %>
                            </div>
                        </div>
                        <div class="count-label">Total Revenue</div>
                        <div class="action-content">
                            <h4>Total Revenue</h4>
                            <p id="totalRevenueText">Total revenue generated in <span class="period-text"><%= salesData.reportPeriod %></span>: ₹<%= salesData.revenue.total.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></p>
                            <div class="metric-info">
                                <i class="fas fa-chart-line text-success"></i>
                                <span>All Sources</span>
                            </div>
                        </div>
                    </div>

                    <div class="action-card">
                        <div class="action-count">
                            <div class="count-number" id="subscriptionRevenueCount">
                                <%
                                    const subscriptionRevenue = salesData.revenue.subscriptions;
                                    let abbreviatedSubscription = '';
                                    if (subscriptionRevenue >= 10000000) {
                                        abbreviatedSubscription = '₹' + (subscriptionRevenue / 10000000).toFixed(1) + 'Cr';
                                    } else if (subscriptionRevenue >= 100000) {
                                        abbreviatedSubscription = '₹' + (subscriptionRevenue / 100000).toFixed(1) + 'L';
                                    } else if (subscriptionRevenue >= 1000) {
                                        abbreviatedSubscription = '₹' + (subscriptionRevenue / 1000).toFixed(1) + 'k';
                                    } else {
                                        abbreviatedSubscription = '₹' + subscriptionRevenue.toFixed(2);
                                    }
                                %>
                                <%= abbreviatedSubscription %>
                            </div>
                        </div>
                        <div class="count-label">Subscription Revenue</div>
                        <div class="action-content">
                            <h4>Subscription Revenue</h4>
                            <p id="subscriptionRevenueText">Revenue from active subscriptions in <span class="period-text"><%= salesData.reportPeriod %></span>: ₹<%= salesData.revenue.subscriptions.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></p>
                            <div class="metric-info">
                                <i class="fas fa-credit-card text-primary"></i>
                                <span>Subscriptions</span>
                            </div>
                        </div>
                    </div>

                    <div class="action-card">
                        <div class="action-count">
                            <div class="count-number" id="addonRevenueCount">
                                <%
                                    const addonRevenue = salesData.revenue.addons;
                                    let abbreviatedAddon = '';
                                    if (addonRevenue >= 10000000) {
                                        abbreviatedAddon = '₹' + (addonRevenue / 10000000).toFixed(1) + 'Cr';
                                    } else if (addonRevenue >= 100000) {
                                        abbreviatedAddon = '₹' + (addonRevenue / 100000).toFixed(1) + 'L';
                                    } else if (addonRevenue >= 1000) {
                                        abbreviatedAddon = '₹' + (addonRevenue / 1000).toFixed(1) + 'k';
                                    } else {
                                        abbreviatedAddon = '₹' + addonRevenue.toFixed(2);
                                    }
                                %>
                                <%= abbreviatedAddon %>
                            </div>
                        </div>
                        <div class="count-label">Add-on Revenue</div>
                        <div class="action-content">
                            <h4>Add-on Revenue</h4>
                            <p id="addonRevenueText">Revenue from add-ons purchased in <span class="period-text"><%= salesData.reportPeriod %></span>: ₹<%= salesData.revenue.addons.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></p>
                            <div class="metric-info">
                                <i class="fas fa-plus-circle text-info"></i>
                                <span>Add-ons</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Summary Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-info-circle"></i>
                Report Summary
            </h3>
        </div>
        <div class="section-content">
            <div class="report-summary">
                <div class="summary-item">
                    <strong>Report Period:</strong> <%= salesData.reportPeriod %>
                </div>
                <div class="summary-item">
                    <strong>Generated At:</strong> <%= new Date(salesData.generatedAt).toLocaleString() %>
                </div>
                <div class="summary-item">
                    <strong>Total Metrics:</strong> 6 key performance indicators
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const monthSelect = document.getElementById('monthSelect');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const applyFilters = document.getElementById('applyFilters');
    const clearFilters = document.getElementById('clearFilters');
    const refreshReports = document.getElementById('refreshReports');

    // Clear month when date range is selected
    startDate.addEventListener('change', function() {
        if (this.value) {
            monthSelect.value = '';
        }
    });

    endDate.addEventListener('change', function() {
        if (this.value) {
            monthSelect.value = '';
        }
    });

    // Clear date range when month is selected
    monthSelect.addEventListener('change', function() {
        if (this.value) {
            startDate.value = '';
            endDate.value = '';
        }
    });

    // Apply filters
    applyFilters.addEventListener('click', function() {
        const month = monthSelect.value;
        const start = startDate.value;
        const end = endDate.value;

        // Validate date range
        if (start && end) {
            const startDateObj = new Date(start);
            const endDateObj = new Date(end);
            const diffMonths = (endDateObj.getFullYear() - startDateObj.getFullYear()) * 12 +
                              (endDateObj.getMonth() - startDateObj.getMonth());

            if (diffMonths > 6) {
                alert('Date range cannot exceed 6 months');
                return;
            }

            if (startDateObj > endDateObj) {
                alert('Start date cannot be after end date');
                return;
            }
        }

        // Build query parameters
        const params = new URLSearchParams();
        if (month) {
            params.append('month', month);
        } else if (start && end) {
            params.append('startDate', start);
            params.append('endDate', end);
        }

        // Redirect with filters
        window.location.href = '/admin/sales-reports?' + params.toString();
    });

    // Clear filters
    clearFilters.addEventListener('click', function() {
        window.location.href = '/admin/sales-reports';
    });

    // Refresh reports
    refreshReports.addEventListener('click', function() {
        window.location.reload();
    });
});
</script>

<style>
.filter-controls {
    background: var(--card-bg);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.filter-row {
    display: flex;
    align-items: flex-start;
    gap: 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 250px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

.filter-input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--input-bg);
    color: var(--text-primary);
    font-size: 14px;
    width: 100%;
}

.filter-separator {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-weight: 600;
    font-size: 14px;
    margin-top: 28px;
    padding: 0 10px;
}

.date-range-group {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.date-range-group .filter-input {
    width: auto;
    min-width: 140px;
}

.date-separator {
    color: var(--text-muted);
    font-weight: 500;
}

.filter-actions {
    display: flex;
    gap: 12px;
}

@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        gap: 20px;
    }

    .filter-separator {
        margin-top: 0;
        margin-bottom: 10px;
    }

    .filter-group {
        min-width: 100%;
    }
}

.report-period {
    font-size: 14px;
    color: var(--text-muted);
    font-weight: normal;
}

.metric-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    font-size: 14px;
}

.text-success { color: #28a745; }
.text-info { color: #17a2b8; }
.text-danger { color: #dc3545; }

.report-summary {
    background: var(--card-bg);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.summary-item {
    margin-bottom: 12px;
    color: var(--text-primary);
}

.summary-item:last-child {
    margin-bottom: 0;
}

.section-actions {
    display: flex;
    gap: 8px;
}

.period-text {
    font-weight: 600;
    color: var(--primary-color);
}
</style>

<%-include("../partials/footer")%>
