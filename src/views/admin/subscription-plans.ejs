<%-include("../partials/header")%>

<!-- Subscription Plans Content -->
<div class="dashboard">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="page-header-content">
            <div>
                <h2>Subscription Plans</h2>
                <p>View all available subscription plans with details</p>
            </div>
            <div class="page-actions">
                <a href="/admin/subscriptions" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Subscriptions
                </a>
            </div>
        </div>
    </div>

    <!-- Plans Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-list-alt"></i>
                All Subscription Plans (<%= plans.length %>)
            </h3>
        </div>
        <div class="section-content">
            <% if (plans && plans.length > 0) { %>
                <div class="plans-grid">
                    <% plans.forEach(plan => { %>
                        <div class="plan-card <%= plan.plan_type.toLowerCase() %>">
                            <div class="plan-header">
                                <div class="plan-type-badge">
                                    <%= plan.plan_type %>
                                </div>
                                <h4 class="plan-name"><%= plan.name %></h4>
                                <div class="plan-price">
                                    <span class="currency"><%= plan.currency %></span>
                                    <span class="amount"><%= plan.price %></span>
                                    <span class="cycle">/ <%= plan.billing_cycle.toLowerCase() %></span>
                                </div>
                            </div>
                            
                            <div class="plan-body">
                                <% if (plan.description) { %>
                                    <p class="plan-description"><%= plan.description %></p>
                                <% } %>
                                
                                <div class="plan-credits">
                                    <% if (plan.is_unlimited_credits) { %>
                                        <span class="credits unlimited">
                                            <i class="fas fa-infinity"></i>
                                            Unlimited Credits
                                        </span>
                                    <% } else { %>
                                        <span class="credits limited">
                                            <i class="fas fa-coins"></i>
                                            <%= plan.credits %> Credits
                                        </span>
                                    <% } %>
                                </div>
                                
                                <% if (plan.features) { %>
                                    <div class="plan-features">
                                        <h5>Features:</h5>
                                        <div class="features-json">
                                            <pre><%= JSON.stringify(JSON.parse(plan.features), null, 2) %></pre>
                                        </div>
                                    </div>
                                <% } %>
                                
                                <% if (plan.limits) { %>
                                    <div class="plan-limits">
                                        <h5>Limits:</h5>
                                        <div class="limits-json">
                                            <pre><%= JSON.stringify(JSON.parse(plan.limits), null, 2) %></pre>
                                        </div>
                                    </div>
                                <% } %>
                            </div>
                            
                            <div class="plan-footer">
                                <div class="plan-status">
                                    <span class="status-badge <%= plan.is_active ? 'active' : 'inactive' %>">
                                        <i class="fas fa-circle"></i>
                                        <%= plan.is_active ? 'Active' : 'Inactive' %>
                                    </span>
                                </div>
                                <div class="plan-meta">
                                    <small>Sort Order: <%= plan.sort_order %></small>
                                    <small>Created: <%= new Date(plan.created_at).toLocaleDateString() %></small>
                                </div>
                            </div>
                        </div>
                    <% }); %>
                </div>
            <% } else { %>
                <div class="empty-state">
                    <i class="fas fa-list-alt" style="font-size: 48px; color: var(--text-muted); margin-bottom: 16px;"></i>
                    <h4>No Subscription Plans Found</h4>
                    <p>There are no subscription plans available at the moment.</p>
                </div>
            <% } %>
        </div>
    </div>
</div>

<style>
/* Subscription Plans Specific Styles */

.page-actions .btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn.btn-secondary:hover {
    background: var(--hover-bg);
    border-color: var(--accent-color);
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    margin-top: 20px;
}

.plan-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.plan-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--accent-color);
}

.plan-card.explorer {
    border-top: 3px solid #10b981;
}

.plan-card.creator {
    border-top: 3px solid #3b82f6;
}

.plan-card.pro {
    border-top: 3px solid #8b5cf6;
}

.plan-card.addon {
    border-top: 3px solid #f59e0b;
}

.plan-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.plan-type-badge {
    display: inline-block;
    padding: 4px 8px;
    background: var(--accent-color);
    color: #000;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 8px;
}

.plan-name {
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
}

.plan-price {
    display: flex;
    align-items: baseline;
    gap: 4px;
}

.plan-price .currency {
    color: var(--text-secondary);
    font-size: 14px;
}

.plan-price .amount {
    color: var(--text-primary);
    font-size: 24px;
    font-weight: 700;
}

.plan-price .cycle {
    color: var(--text-secondary);
    font-size: 14px;
}

.plan-body {
    padding: 20px;
}

.plan-description {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 16px;
}

.plan-credits {
    margin-bottom: 16px;
}

.credits {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
}

.credits.unlimited {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.credits.limited {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.plan-features, .plan-limits {
    margin-bottom: 16px;
}

.plan-features h5, .plan-limits h5 {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
}

.features-json, .limits-json {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    overflow-x: auto;
}

.features-json pre, .limits-json pre {
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
}

.plan-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
    background: var(--secondary-bg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.status-badge.inactive {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.plan-meta {
    display: flex;
    flex-direction: column;
    gap: 2px;
    text-align: right;
}

.plan-meta small {
    color: var(--text-muted);
    font-size: 11px;
}

@media (max-width: 768px) {
    .plans-grid {
        grid-template-columns: 1fr;
    }
    
    .page-header-content {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .plan-footer {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
    
    .plan-meta {
        text-align: left;
    }
}
</style>

<%-include("../partials/footer")%>
