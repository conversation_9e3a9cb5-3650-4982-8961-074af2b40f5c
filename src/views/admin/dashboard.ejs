<%-include("../partials/header")%>

<!-- Dashboard Content -->
<div class="dashboard">
    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-card__icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-card__content">
                <div class="stat-card__value"><%= stats ? stats.totalUsers : '0' %></div>
                <div class="stat-card__label">Total Users</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card__icon">
                <i class="fas fa-credit-card"></i>
            </div>
            <div class="stat-card__content">
                <div class="stat-card__value"><%= stats ? stats.totalSubscriptions : '0' %></div>
                <div class="stat-card__label">Active Subscriptions</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card__icon">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="stat-card__content">
                <div class="stat-card__value"><%= stats ? stats.newUsersThisMonth : '0' %></div>
                <div class="stat-card__label">New Users This Month</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card__icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-card__content">
                <div class="stat-card__value">₹<%= stats ? (stats.revenueThisMonth || 0).toLocaleString() : '0' %></div>
                <div class="stat-card__label">Revenue This Month</div>
            </div>
        </div>
    </div>

    <!-- Dashboard Sections -->
    <div class="dashboard-grid">
        <!-- Recent Activity -->
        <div class="dashboard-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-clock"></i>
                    Recent Activity
                </h3>
                <button class="btn btn-secondary btn-sm" onclick="refreshRecentActivity()">
                    <i class="fas fa-refresh"></i>
                    Refresh
                </button>
            </div>
            <div class="section-content">
                <div class="activity-list">
                    <% if (stats && stats.recentUsers && stats.recentUsers.length > 0) { %>
                        <% stats.recentUsers.forEach(function(user) { %>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">New user registered</div>
                                    <div class="activity-details"><%= user.name %> (<%= user.email %>)</div>
                                    <div class="activity-time"><%= new Date(user.created_at).toLocaleDateString('en-US', {
                                        year: 'numeric',
                                        month: 'short',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    }) %></div>
                                </div>
                            </div>
                        <% }); %>
                    <% } else { %>
                        <div class="empty-state">
                            <i class="fas fa-info-circle"></i>
                            <p>No recent user registrations</p>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </h3>
            </div>
            <div class="section-content">
                <div class="quick-actions">
                    <a href="/admin/subscriptions" class="quick-action-btn">
                        <i class="fas fa-credit-card"></i>
                        <span>Manage Subscriptions</span>
                    </a>
                    <a href="/admin/user-roles" class="quick-action-btn">
                        <i class="fas fa-users-cog"></i>
                        <span>User Roles</span>
                    </a>
                    <a href="/admin/sales-reports" class="quick-action-btn">
                        <i class="fas fa-chart-line"></i>
                        <span>View Reports</span>
                    </a>
                    <a href="/admin/support" class="quick-action-btn">
                        <i class="fas fa-headset"></i>
                        <span>Support Tickets</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="dashboard-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-server"></i>
                    System Status
                </h3>
                <span class="status-indicator status-indicator--online">
                    <i class="fas fa-circle"></i>
                    Online
                </span>
            </div>
            <div class="section-content">
                <div class="system-stats">
                    <div class="system-stat">
                        <div class="system-stat__label">Database</div>
                        <div class="system-stat__value">
                            <span class="status-indicator status-indicator--online">
                                <i class="fas fa-circle"></i>
                                Connected
                            </span>
                        </div>
                    </div>
                    <div class="system-stat">
                        <div class="system-stat__label">Last Updated</div>
                        <div class="system-stat__value"><%= stats ? new Date(stats.lastUpdated).toLocaleString() : 'Just now' %></div>
                    </div>
                    <div class="system-stat">
                        <div class="system-stat__label">Server Uptime</div>
                        <div class="system-stat__value">
                            <span id="uptime">Calculating...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Dashboard-specific JavaScript
    function initializePage() {
        console.log('📊 Initializing dashboard...');
        
        // Update uptime
        updateUptime();
        setInterval(updateUptime, 60000); // Update every minute
        
        // Auto-refresh stats every 5 minutes
        setInterval(refreshDashboardStats, 5 * 60 * 1000);
    }

    function updateUptime() {
        // Simple uptime calculation (this would be better served from the server)
        const startTime = sessionStorage.getItem('dashboardStartTime');
        if (!startTime) {
            sessionStorage.setItem('dashboardStartTime', Date.now());
            document.getElementById('uptime').textContent = '0 minutes';
            return;
        }
        
        const uptime = Math.floor((Date.now() - parseInt(startTime)) / 60000);
        document.getElementById('uptime').textContent = uptime + ' minutes';
    }

    function refreshDashboardStats() {
        console.log('🔄 Refreshing dashboard stats...');
        showLoading();
        
        fetch('/admin/api/dashboard-stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update stats cards
                    updateStatsCards(data.data);
                    console.log('✅ Dashboard stats refreshed');
                } else {
                    console.error('❌ Failed to refresh stats:', data.message);
                }
            })
            .catch(error => {
                console.error('❌ Error refreshing stats:', error);
            })
            .finally(() => {
                hideLoading();
            });
    }

    function updateStatsCards(stats) {
        // Update stat card values
        const statCards = document.querySelectorAll('.stat-card__value');
        if (statCards.length >= 4) {
            statCards[0].textContent = (stats.totalUsers || 0).toLocaleString();
            statCards[1].textContent = (stats.totalSubscriptions || 0).toLocaleString();
            statCards[2].textContent = (stats.newUsersThisMonth || 0).toLocaleString();
            statCards[3].textContent = '₹' + (stats.revenueThisMonth || 0).toLocaleString();
        }

        // Update recent activity if provided
        if (stats.recentUsers && stats.recentUsers.length > 0) {
            updateRecentActivity(stats.recentUsers);
        }
    }

    function updateRecentActivity(recentUsers) {
        const activityList = document.querySelector('.activity-list');
        if (!activityList) return;

        // Clear existing content
        activityList.innerHTML = '';

        if (recentUsers.length === 0) {
            activityList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-info-circle"></i>
                    <p>No recent user registrations</p>
                </div>
            `;
            return;
        }

        // Add recent users
        recentUsers.forEach(user => {
            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';

            const createdDate = new Date(user.created_at);
            const formattedDate = createdDate.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            activityItem.innerHTML = `
                <div class="activity-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">New user registered</div>
                    <div class="activity-details">${user.name} (${user.email})</div>
                    <div class="activity-time">${formattedDate}</div>
                </div>
            `;

            activityList.appendChild(activityItem);
        });
    }

    function refreshRecentActivity() {
        console.log('🔄 Refreshing recent activity...');
        refreshDashboardStats();
    }


</script>

<%-include("../partials/footer")%>
