<%- include("../partials/header", { title: title }) %>

<div class="page-header">
    <div class="page-header__content">
        <div class="page-header__breadcrumb">
            <a href="/admin/support" class="breadcrumb-link">
                <i class="fas fa-headset"></i>
                Support
            </a>
            <i class="fas fa-chevron-right breadcrumb-separator"></i>
            <span class="breadcrumb-current">Ticket Details</span>
        </div>
        <h1 class="page-header__title">
            <i class="fas fa-ticket-alt page-header__icon"></i>
            Ticket #<%= ticket.id.substring(0, 8) %>
        </h1>
        <div class="page-header__actions">
            <a href="/admin/support" class="btn btn--secondary">
                <i class="fas fa-arrow-left"></i>
                Back to Support
            </a>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<% if (typeof query !== 'undefined') { %>
    <% if (query.success === 'reply_added') { %>
        <div class="alert alert--success">
            <i class="fas fa-check-circle"></i>
            Reply added successfully and email sent to user.
        </div>
    <% } else if (query.success === 'status_updated') { %>
        <div class="alert alert--success">
            <i class="fas fa-check-circle"></i>
            Ticket status updated successfully and email sent to user.
        </div>
    <% } else if (query.error === 'reply_failed') { %>
        <div class="alert alert--error">
            <i class="fas fa-exclamation-circle"></i>
            Failed to add reply. Please try again.
        </div>
    <% } else if (query.error === 'status_update_failed') { %>
        <div class="alert alert--error">
            <i class="fas fa-exclamation-circle"></i>
            Failed to update ticket status. Please try again.
        </div>
    <% } %>
<% } %>

<div class="ticket-details">
    <!-- Ticket Header -->
    <div class="ticket-header">
        <div class="ticket-header__main">
            <h2 class="ticket-header__subject"><%= ticket.subject %></h2>
            <div class="ticket-header__meta">
                <span class="ticket-meta">
                    <i class="fas fa-calendar-alt"></i>
                    Created: <%= new Date(ticket.created_at).toLocaleString() %>
                </span>
                <span class="ticket-meta">
                    <i class="fas fa-clock"></i>
                    Updated: <%= new Date(ticket.updated_at).toLocaleString() %>
                </span>
            </div>
        </div>
        <div class="ticket-header__badges">
            <span class="priority-badge priority-<%= ticket.priority.toLowerCase() %>">
                <%= ticket.priority %>
            </span>
            <span class="status-badge status-<%= ticket.status.toLowerCase().replace('_', '-') %>">
                <%= ticket.status.replace('_', ' ') %>
            </span>
        </div>
    </div>

    <!-- User Information -->
    <div class="ticket-section">
        <h3 class="ticket-section__title">
            <i class="fas fa-user"></i>
            User Information
        </h3>
        <div class="user-details">
            <div class="user-details__item">
                <label>Name:</label>
                <span><%= ticket.first_name || '' %> <%= ticket.last_name || '' %></span>
            </div>
            <div class="user-details__item">
                <label>Email:</label>
                <span><%= ticket.user_email %></span>
            </div>
            <% if (ticket.user_mobile) { %>
                <div class="user-details__item">
                    <label>Mobile:</label>
                    <span><%= ticket.user_mobile %></span>
                </div>
            <% } %>
            <div class="user-details__item">
                <label>User Status:</label>
                <span class="<%= ticket.user_active ? 'text-success' : 'text-danger' %>">
                    <%= ticket.user_active ? 'Active' : 'Inactive' %>
                </span>
            </div>
        </div>
    </div>

    <!-- Ticket Content -->
    <div class="ticket-section">
        <h3 class="ticket-section__title">
            <i class="fas fa-file-alt"></i>
            Description
        </h3>
        <div class="ticket-content">
            <p><%= ticket.description %></p>
        </div>
    </div>

    <!-- Technical Details -->
    <% if (ticket.tech_details) { %>
        <div class="ticket-section">
            <h3 class="ticket-section__title">
                <i class="fas fa-cogs"></i>
                Technical Details
            </h3>
            <div class="ticket-content">
                <pre><%= ticket.tech_details %></pre>
            </div>
        </div>
    <% } %>

    <!-- Attachment -->
    <% if (ticket.attachment_name) { %>
        <div class="ticket-section">
            <h3 class="ticket-section__title">
                <i class="fas fa-paperclip"></i>
                Attachment
            </h3>
            <div class="attachment-info">
                <div class="attachment-details">
                    <div class="attachment-name">
                        <i class="fas fa-file"></i>
                        <%= ticket.attachment_name %>
                    </div>
                    <div class="attachment-meta">
                        Type: <%= ticket.attachment_type || 'Unknown' %> | 
                        Size: <%= ticket.attachment_size ? (ticket.attachment_size / 1024).toFixed(1) + ' KB' : 'Unknown' %>
                    </div>
                </div>
                <% if (ticket.attachment_secure_id) { %>
                    <a href="<%= ticket.main_app_url %>/files/<%= ticket.attachment_secure_id %>" class="btn btn--small btn--secondary" target="_blank">
                        <i class="fas fa-download"></i>
                        Download
                    </a>
                <% } else { %>
                    <span class="text-muted">File not available for download</span>
                <% } %>
            </div>
        </div>
    <% } %>

    <!-- Admin Actions -->
    <div class="ticket-actions">
        <div class="ticket-actions__section">
            <h3 class="ticket-section__title">
                <i class="fas fa-reply"></i>
                Add Reply
            </h3>
            <form id="replyForm" class="reply-form">
                <div class="form-group">
                    <label for="replyMessage" class="form-label">Reply Message</label>
                    <textarea
                        id="replyMessage"
                        name="replyMessage"
                        class="form-textarea"
                        rows="6"
                        placeholder="Type your reply to the user here..."
                        required
                    ></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" id="sendReplyBtn" class="btn btn--primary">
                        <i class="fas fa-paper-plane"></i>
                        Send Reply
                    </button>
                </div>
            </form>
        </div>

        <div class="ticket-actions__section">
            <h3 class="ticket-section__title">
                <i class="fas fa-edit"></i>
                Update Status
            </h3>
            <form id="statusForm" class="status-form">
                <div class="form-group">
                    <label for="status" class="form-label">New Status</label>
                    <select id="status" name="status" class="form-select" required>
                        <option value="">Select Status</option>
                        <option value="OPEN" <%= ticket.status === 'OPEN' ? 'selected' : '' %>>Open</option>
                        <option value="IN_PROGRESS" <%= ticket.status === 'IN_PROGRESS' ? 'selected' : '' %>>In Progress</option>
                        <option value="RESOLVED" <%= ticket.status === 'RESOLVED' ? 'selected' : '' %>>Resolved</option>
                        <option value="CLOSED" <%= ticket.status === 'CLOSED' ? 'selected' : '' %>>Closed</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="submit" id="updateStatusBtn" class="btn btn--warning">
                        <i class="fas fa-save"></i>
                        Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.ticket-details {
    max-width: 1200px;
    margin: 0 auto;
}

.ticket-header {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.ticket-header__subject {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 12px 0;
}

.ticket-header__meta {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}

.ticket-meta {
    color: var(--text-secondary);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 6px;
}

.ticket-header__badges {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.ticket-section {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
}

.ticket-section__title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.ticket-content {
    color: var(--text-primary);
    line-height: 1.6;
}

.ticket-content pre {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 16px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.user-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.user-details__item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.user-details__item label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.user-details__item span {
    color: var(--text-primary);
}

.attachment-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
}

.attachment-name {
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.attachment-meta {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.ticket-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.ticket-actions__section {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 24px;
}

.reply-form, .status-form {
    margin-top: 16px;
}

.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.form-textarea, .form-select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--input-bg);
    color: var(--text-primary);
    font-family: inherit;
    resize: vertical;
}

.form-textarea:focus, .form-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(252, 212, 105, 0.1);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
}

.priority-badge, .status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-urgent { background: #fee; color: #c53030; }
.priority-high { background: #fef5e7; color: #dd6b20; }
.priority-medium { background: #ebf8ff; color: #3182ce; }
.priority-low { background: #f0fff4; color: #38a169; }

.status-open { background: #ebf8ff; color: #3182ce; }
.status-in-progress { background: #fef5e7; color: #dd6b20; }
.status-resolved { background: #f0fff4; color: #38a169; }
.status-closed { background: #f7fafc; color: #718096; }

@media (max-width: 768px) {
    .ticket-header {
        flex-direction: column;
        gap: 16px;
    }
    
    .ticket-actions {
        grid-template-columns: 1fr;
    }
    
    .user-details {
        grid-template-columns: 1fr;
    }
    
    .attachment-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const ticketId = '<%= ticket.id %>';
    const replyForm = document.getElementById('replyForm');
    const statusForm = document.getElementById('statusForm');
    const sendReplyBtn = document.getElementById('sendReplyBtn');
    const updateStatusBtn = document.getElementById('updateStatusBtn');

    // Handle reply form submission
    replyForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const replyMessage = document.getElementById('replyMessage').value.trim();

        if (!replyMessage) {
            showNotification('Please enter a reply message', 'error');
            return;
        }

        // Show loading state
        showLoading('Sending reply...');
        setButtonLoading(sendReplyBtn, true, 'Sending...');

        try {
            const response = await fetch(`/admin/api/support/tickets/${ticketId}/reply`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ replyMessage })
            });

            const result = await response.json();

            if (result.success) {
                showNotification('Reply sent successfully! Email notification sent to user.', 'success');
                document.getElementById('replyMessage').value = ''; // Clear the form

                // Optionally reload the page after a short delay to show updated ticket
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification(result.message || 'Failed to send reply', 'error');
            }
        } catch (error) {
            console.error('Error sending reply:', error);
            showNotification('An error occurred while sending the reply', 'error');
        } finally {
            hideLoading();
            setButtonLoading(sendReplyBtn, false);
        }
    });

    // Handle status form submission
    statusForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const status = document.getElementById('status').value;

        if (!status) {
            showNotification('Please select a status', 'error');
            return;
        }

        // Show loading state
        showLoading('Updating status...');
        setButtonLoading(updateStatusBtn, true, 'Updating...');

        try {
            const response = await fetch(`/admin/api/support/tickets/${ticketId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status })
            });

            const result = await response.json();

            if (result.success) {
                showNotification('Status updated successfully! Email notification sent to user.', 'success');

                // Reload the page after a short delay to show updated status
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification(result.message || 'Failed to update status', 'error');
            }
        } catch (error) {
            console.error('Error updating status:', error);
            showNotification('An error occurred while updating the status', 'error');
        } finally {
            hideLoading();
            setButtonLoading(updateStatusBtn, false);
        }
    });

    // Helper function to set button loading state
    function setButtonLoading(button, loading, loadingText = 'Loading...') {
        if (loading) {
            button.disabled = true;
            button.dataset.originalHtml = button.innerHTML;
            button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${loadingText}`;
        } else {
            button.disabled = false;
            if (button.dataset.originalHtml) {
                button.innerHTML = button.dataset.originalHtml;
            }
        }
    }

    // Use global functions from admin.js
    function showLoading(message) {
        if (window.adminDashboard && window.adminDashboard.showLoading) {
            window.adminDashboard.showLoading(message);
        }
    }

    function hideLoading() {
        if (window.adminDashboard && window.adminDashboard.hideLoading) {
            window.adminDashboard.hideLoading();
        }
    }

    function showNotification(message, type) {
        if (window.adminDashboard && window.adminDashboard.showNotification) {
            window.adminDashboard.showNotification(message, type);
        } else {
            // Fallback to alert if admin.js functions are not available
            alert(message);
        }
    }
});
</script>

<%- include("../partials/footer") %>
