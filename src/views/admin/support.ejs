<%- include("../partials/header", { title: title }) %>

<div class="page-header">
    <div class="page-header__content">
        <h1 class="page-header__title">
            <i class="fas fa-headset page-header__icon"></i>
            Support Management
        </h1>
        <p class="page-header__subtitle">Manage customer support tickets and inquiries</p>
    </div>
</div>

<!-- Support Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card stat-card--primary">
        <div class="stat-card__content">
            <div class="stat-card__header">
                <h3 class="stat-card__title">Total Tickets</h3>
                <i class="stat-card__icon fas fa-ticket-alt"></i>
            </div>
            <div class="stat-card__value" id="totalTickets">
                <%= stats.totalTickets %>
            </div>
            <div class="stat-card__label">All time tickets</div>
        </div>
    </div>

    <div class="stat-card stat-card--warning">
        <div class="stat-card__content">
            <div class="stat-card__header">
                <h3 class="stat-card__title">Open Tickets</h3>
                <i class="stat-card__icon fas fa-folder-open"></i>
            </div>
            <div class="stat-card__value" id="openTickets">
                <%= stats.openTickets %>
            </div>
            <div class="stat-card__label">Awaiting response</div>
        </div>
    </div>

    <div class="stat-card stat-card--success">
        <div class="stat-card__content">
            <div class="stat-card__header">
                <h3 class="stat-card__title">Closed Tickets</h3>
                <i class="stat-card__icon fas fa-check-circle"></i>
            </div>
            <div class="stat-card__value" id="closedTickets">
                <%= stats.closedTickets %>
            </div>
            <div class="stat-card__label">Resolved & closed</div>
        </div>
    </div>

    <div class="stat-card stat-card--info">
        <div class="stat-card__content">
            <div class="stat-card__header">
                <h3 class="stat-card__title">In Progress</h3>
                <i class="stat-card__icon fas fa-clock"></i>
            </div>
            <div class="stat-card__value" id="inProgressTickets">
                <%= stats.inProgressTickets %>
            </div>
            <div class="stat-card__label">Being worked on</div>
        </div>
    </div>

    <div class="stat-card stat-card--danger">
        <div class="stat-card__content">
            <div class="stat-card__header">
                <h3 class="stat-card__title">Urgent Tickets</h3>
                <i class="stat-card__icon fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-card__value" id="urgentTickets">
                <%= stats.urgentTickets %>
            </div>
            <div class="stat-card__label">Require immediate attention</div>
        </div>
    </div>
</div>

<!-- Support Tickets Section -->
<div class="content-section">
    <div class="content-section__header">
        <h2 class="content-section__title">
            <i class="fas fa-list content-section__icon"></i>
            Support Tickets
        </h2>
        <div class="content-section__actions">
            <div class="filter-group">
                <select id="statusFilter" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="OPEN">Open</option>
                    <option value="IN_PROGRESS">In Progress</option>
                    <option value="RESOLVED">Resolved</option>
                    <option value="CLOSED">Closed</option>
                </select>
                <select id="priorityFilter" class="form-select">
                    <option value="">All Priorities</option>
                    <option value="URGENT">Urgent</option>
                    <option value="HIGH">High</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="LOW">Low</option>
                </select>
                <button id="refreshTickets" class="btn btn--secondary">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Tickets Table -->
    <div class="table-container">
        <div id="ticketsLoading" class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
            Loading tickets...
        </div>
        
        <div id="ticketsTable" class="table-wrapper" style="display: none;">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Ticket ID</th>
                        <th>Subject</th>
                        <th>User</th>
                        <th>Priority</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="ticketsTableBody">
                    <!-- Tickets will be loaded here via JavaScript -->
                </tbody>
            </table>
        </div>

        <div id="ticketsEmpty" class="empty-state" style="display: none;">
            <i class="fas fa-inbox empty-state__icon"></i>
            <h3 class="empty-state__title">No tickets found</h3>
            <p class="empty-state__message">No support tickets match your current filters.</p>
        </div>

        <!-- Pagination -->
        <div id="ticketsPagination" class="pagination-container" style="display: none;">
            <!-- Pagination will be loaded here via JavaScript -->
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentPage = 1;
    let currentStatus = '';
    let currentPriority = '';

    // Load tickets on page load
    loadTickets();

    // Filter event listeners
    document.getElementById('statusFilter').addEventListener('change', function() {
        currentStatus = this.value;
        currentPage = 1;
        loadTickets();
    });

    document.getElementById('priorityFilter').addEventListener('change', function() {
        currentPriority = this.value;
        currentPage = 1;
        loadTickets();
    });

    document.getElementById('refreshTickets').addEventListener('click', function() {
        loadTickets();
    });

    function loadTickets() {
        const loadingEl = document.getElementById('ticketsLoading');
        const tableEl = document.getElementById('ticketsTable');
        const emptyEl = document.getElementById('ticketsEmpty');
        const paginationEl = document.getElementById('ticketsPagination');

        // Show loading state
        loadingEl.style.display = 'block';
        tableEl.style.display = 'none';
        emptyEl.style.display = 'none';
        paginationEl.style.display = 'none';

        // Build query parameters
        const params = new URLSearchParams({
            page: currentPage,
            limit: 20
        });

        if (currentStatus) params.append('status', currentStatus);
        if (currentPriority) params.append('priority', currentPriority);

        // Fetch tickets
        fetch(`/admin/api/support/tickets?${params}`)
            .then(response => response.json())
            .then(data => {
                loadingEl.style.display = 'none';

                if (data.success && data.data.tickets.length > 0) {
                    renderTicketsTable(data.data.tickets);
                    renderPagination(data.data.pagination);
                    tableEl.style.display = 'block';
                    paginationEl.style.display = 'block';
                } else {
                    emptyEl.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error loading tickets:', error);
                loadingEl.style.display = 'none';
                emptyEl.style.display = 'block';
            });
    }

    function renderTicketsTable(tickets) {
        const tbody = document.getElementById('ticketsTableBody');
        tbody.innerHTML = '';

        tickets.forEach(ticket => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <span class="ticket-id">#${ticket.id.substring(0, 8)}</span>
                </td>
                <td>
                    <div class="ticket-subject">
                        <strong>${escapeHtml(ticket.subject)}</strong>
                        ${ticket.attachment_name ? '<i class="fas fa-paperclip text-muted" title="Has attachment"></i>' : ''}
                    </div>
                </td>
                <td>
                    <div class="user-info">
                        <div class="user-name">${escapeHtml(ticket.first_name || '')} ${escapeHtml(ticket.last_name || '')}</div>
                        <div class="user-email">${escapeHtml(ticket.user_email)}</div>
                    </div>
                </td>
                <td>
                    <span class="priority-badge priority-${ticket.priority.toLowerCase()}">${ticket.priority}</span>
                </td>
                <td>
                    <span class="status-badge status-${ticket.status.toLowerCase().replace('_', '-')}">${formatStatus(ticket.status)}</span>
                </td>
                <td>
                    <div class="date-info">
                        <div class="date">${formatDate(ticket.created_at)}</div>
                        <div class="time">${formatTime(ticket.created_at)}</div>
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <a href="/admin/support/tickets/${ticket.id}" class="btn btn--small btn--primary">
                            <i class="fas fa-eye"></i>
                            View
                        </a>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    function renderPagination(pagination) {
        const container = document.getElementById('ticketsPagination');
        
        if (pagination.totalPages <= 1) {
            container.style.display = 'none';
            return;
        }

        let paginationHTML = '<div class="pagination">';
        
        // Previous button
        if (pagination.hasPreviousPage) {
            paginationHTML += `<button class="pagination__btn" onclick="changePage(${pagination.currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>`;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.currentPage - 2);
        const endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === pagination.currentPage;
            paginationHTML += `<button class="pagination__btn ${isActive ? 'pagination__btn--active' : ''}" 
                onclick="changePage(${i})">${i}</button>`;
        }

        // Next button
        if (pagination.hasNextPage) {
            paginationHTML += `<button class="pagination__btn" onclick="changePage(${pagination.currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>`;
        }

        paginationHTML += '</div>';
        paginationHTML += `<div class="pagination-info">
            Showing ${(pagination.currentPage - 1) * pagination.itemsPerPage + 1} to 
            ${Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} 
            of ${pagination.totalItems} tickets
        </div>`;

        container.innerHTML = paginationHTML;
    }

    // Global function for pagination
    window.changePage = function(page) {
        currentPage = page;
        loadTickets();
    };

    // Utility functions
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatStatus(status) {
        const statusMap = {
            'OPEN': 'Open',
            'IN_PROGRESS': 'In Progress',
            'RESOLVED': 'Resolved',
            'CLOSED': 'Closed'
        };
        return statusMap[status] || status;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    function formatTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
});
</script>

<%- include("../partials/footer") %>
