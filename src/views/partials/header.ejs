<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | TheInfini AI Admin</title>
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/favicon_io/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon_io/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon_io/favicon-16x16.png">
    <link rel="icon" type="image/x-icon" href="/assets/favicon_io/favicon.ico">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="/css/admin.css">
    
    <!-- Additional page-specific CSS -->
    <% if (typeof additionalCSS !== 'undefined') { %>
        <% additionalCSS.forEach(function(css) { %>
            <link rel="stylesheet" href="<%= css %>">
        <% }); %>
    <% } %>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <%-include("../components/sidebar")%>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="topbar">
                <div class="topbar__left">
                    <button class="topbar__menu-btn" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="topbar__title"><%= title %></h1>
                </div>
                <div class="topbar__right">
                    <div class="topbar__user">
                        <span class="topbar__user-name">
                            <% if (isAuthenticated && user) { %>
                                <%= user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.email || 'Admin User' %>
                            <% } else { %>
                                Not Authenticated
                            <% } %>
                        </span>
                        <div class="topbar__user-avatar">
                            <% if (isAuthenticated && user) { %>
                                <%= (user.firstName && user.firstName.charAt(0).toUpperCase()) || (user.email && user.email.charAt(0).toUpperCase()) || 'A' %>
                            <% } else { %>
                                ?
                            <% } %>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="content">
