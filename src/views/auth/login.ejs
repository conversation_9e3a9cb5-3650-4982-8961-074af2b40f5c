<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/favicon_io/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon_io/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon_io/favicon-16x16.png">
    <link rel="icon" type="image/x-icon" href="/assets/favicon_io/favicon.ico">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="/css/admin.css">
    
    <style>
        /* Login Page Specific Styles */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-bg);
            padding: 20px;
        }

        .login-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 40px;
            width: 100%;
            max-width: 420px;
            box-shadow: var(--shadow-heavy);
        }

        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .login-header__logo {
            color: var(--accent-color);
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .login-header__title {
            color: var(--text-primary);
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .login-header__subtitle {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-label {
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 500;
        }

        .form-input {
            background: var(--secondary-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(252, 212, 105, 0.1);
        }

        .form-input::placeholder {
            color: var(--text-muted);
        }

        .password-input-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .password-input {
            padding-right: 50px;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-muted);
            font-size: 16px;
            padding: 8px;
            border-radius: 4px;
            transition: color 0.3s ease;
            z-index: 1;
        }

        .password-toggle:hover {
            color: var(--accent-color);
        }

        .password-toggle:focus {
            outline: none;
            color: var(--accent-color);
        }

        .login-button {
            background: var(--accent-color);
            color: var(--primary-bg);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 8px;
        }

        .login-button:hover {
            background: var(--accent-hover);
            transform: translateY(-1px);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .alert--error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: #fca5a5;
        }

        .alert--success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            color: #6ee7b7;
        }

        .login-footer {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid var(--border-color);
        }

        .login-footer__text {
            color: var(--text-secondary);
            font-size: 12px;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid var(--primary-bg);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .login-button.loading .loading-spinner {
            display: inline-block;
        }

        .login-button.loading .button-text {
            opacity: 0.7;
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .login-card {
                padding: 24px;
                margin: 10px;
            }
            
            .login-header__title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-header__logo">
                    <img src="/assets/images/infini-internals-logo.svg" alt="The Infini AI" style="height: 40px; width: auto;">
                </div>
                <h1 class="login-header__title">Admin Login</h1>
                <p class="login-header__subtitle">Sign in to access the admin dashboard</p>
            </div>

            <!-- Error/Success Messages -->
            <% if (error) { %>
                <div class="alert alert--error">
                    <i class="fas fa-exclamation-circle"></i>
                    <%= error %>
                </div>
            <% } %>

            <% if (success) { %>
                <div class="alert alert--success">
                    <i class="fas fa-check-circle"></i>
                    <%= success %>
                </div>
            <% } %>

            <!-- Login Form -->
            <form class="login-form" method="POST" action="/admin/auth/login" id="loginForm">
                <!-- CSRF Token -->
                <input type="hidden" name="csrfToken" value="<%= csrfToken %>">

                <div class="form-group">
                    <label for="identifier" class="form-label">Email or Mobile</label>
                    <input 
                        type="text" 
                        id="identifier" 
                        name="identifier" 
                        class="form-input" 
                        placeholder="Enter your email or mobile number"
                        required
                        autocomplete="username"
                    >
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="password-input-container">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="form-input password-input"
                            placeholder="Enter your password"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="login-button" id="loginButton">
                    <span class="loading-spinner"></span>
                    <span class="button-text">Sign In</span>
                </button>
            </form>

            <div class="login-footer">
                <p class="login-footer__text">
                    © <%= new Date().getFullYear() %> The Infini AI. Admin Dashboard.
                </p>
            </div>
        </div>
    </div>

    <script>
        // Login form handling
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const button = document.getElementById('loginButton');
            const identifier = document.getElementById('identifier').value.trim();
            const password = document.getElementById('password').value;

            // Basic validation
            if (!identifier || !password) {
                e.preventDefault();
                alert('Please fill in all fields');
                return;
            }

            // Show loading state
            button.classList.add('loading');
            button.disabled = true;
        });

        // Password toggle functionality
        const passwordToggle = document.getElementById('passwordToggle');
        const passwordInput = document.getElementById('password');
        const passwordToggleIcon = document.getElementById('passwordToggleIcon');

        passwordToggle.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle icon
            if (type === 'text') {
                passwordToggleIcon.classList.remove('fa-eye');
                passwordToggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordToggleIcon.classList.remove('fa-eye-slash');
                passwordToggleIcon.classList.add('fa-eye');
            }
        });

        // Auto-focus on identifier field
        document.getElementById('identifier').focus();

        // Handle URL parameters for messages
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');

        if (message) {
            let alertDiv = document.createElement('div');
            let alertContent = '';

            switch(message) {
                case 'logged_out':
                    alertDiv.className = 'alert alert--success';
                    alertContent = '<i class="fas fa-check-circle"></i> You have been logged out successfully.';
                    break;
                case 'login_required':
                    alertDiv.className = 'alert alert--error';
                    alertContent = '<i class="fas fa-exclamation-circle"></i> Please log in to access the admin dashboard.';
                    break;
                case 'access_denied':
                    alertDiv.className = 'alert alert--error';
                    alertContent = '<i class="fas fa-lock"></i> Access denied. You need Super Administrator privileges to access this system.';
                    break;
                case 'error':
                    alertDiv.className = 'alert alert--error';
                    alertContent = '<i class="fas fa-exclamation-triangle"></i> An error occurred. Please try logging in again.';
                    break;
                default:
                    alertDiv = null;
            }

            if (alertDiv) {
                alertDiv.innerHTML = alertContent;
                const form = document.querySelector('.login-form');
                form.parentNode.insertBefore(alertDiv, form);
            }

            // Remove message from URL
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    </script>
</body>
</html>
