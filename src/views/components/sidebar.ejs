<aside class="sidebar" id="sidebar">
    <div class="sidebar__content">
        <!-- Header -->
        <div class="sidebar__header">
            <div class="sidebar__logo">
                <img src="/assets/images/infini-logo.svg" alt="The Infini AI" style="height: 32px; width: auto; margin-bottom: 4px;">
                <span class="sidebar__logo-subtitle">Internals</span>
            </div>
            <button class="sidebar__toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Navigation -->
        <nav class="sidebar__nav">
            <ul class="sidebar__nav-list">
                <li>
                    <a href="/admin/dashboard" class="sidebar__nav-item <%= currentPage === 'dashboard' ? 'sidebar__nav-item--active' : '' %>">
                        <i class="sidebar__nav-icon fas fa-tachometer-alt"></i>
                        <span class="sidebar__nav-label">Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="/admin/subscriptions" class="sidebar__nav-item <%= currentPage === 'subscriptions' ? 'sidebar__nav-item--active' : '' %>">
                        <i class="sidebar__nav-icon fas fa-credit-card"></i>
                        <span class="sidebar__nav-label">Subscriptions</span>
                    </a>
                </li>
                <li>
                    <a href="/admin/user-roles" class="sidebar__nav-item <%= currentPage === 'user-roles' ? 'sidebar__nav-item--active' : '' %>">
                        <i class="sidebar__nav-icon fas fa-users-cog"></i>
                        <span class="sidebar__nav-label">User Roles</span>
                    </a>
                </li>
                <li>
                    <a href="/admin/sales-reports" class="sidebar__nav-item <%= currentPage === 'sales-reports' ? 'sidebar__nav-item--active' : '' %>">
                        <i class="sidebar__nav-icon fas fa-chart-line"></i>
                        <span class="sidebar__nav-label">Sales Reports</span>
                    </a>
                </li>
                <li>
                    <a href="/admin/support" class="sidebar__nav-item <%= currentPage === 'support' ? 'sidebar__nav-item--active' : '' %>">
                        <i class="sidebar__nav-icon fas fa-headset"></i>
                        <span class="sidebar__nav-label">Support</span>
                        <% if (typeof openTicketsCount !== 'undefined' && openTicketsCount > 0) { %>
                            <span class="sidebar__nav-badge"><%= openTicketsCount %></span>
                        <% } %>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Bottom Section -->
        <div class="sidebar__bottom">
            <% if (isAuthenticated && user) { %>
                <div class="sidebar__user-profile">
                    <div class="sidebar__user-avatar">
                        <div class="sidebar__user-avatar-placeholder">
                            <%= (user.firstName && user.firstName.charAt(0).toUpperCase()) || (user.email && user.email.charAt(0).toUpperCase()) || 'A' %>
                        </div>
                    </div>
                    <div class="sidebar__user-info">
                        <div class="sidebar__user-name">
                            <%= user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.email || 'Admin User' %>
                        </div>
                        <div class="sidebar__user-plan">Administrator</div>
                    </div>
                    <button class="sidebar__logout-btn" onclick="logout()" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            <% } else { %>
                <div class="sidebar__user-profile">
                    <div class="sidebar__user-avatar">
                        <div class="sidebar__user-avatar-placeholder">?</div>
                    </div>
                    <div class="sidebar__user-info">
                        <div class="sidebar__user-name">Not Authenticated</div>
                        <div class="sidebar__user-plan">Please Login</div>
                    </div>
                </div>
            <% } %>
        </div>
    </div>
</aside>
