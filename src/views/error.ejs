<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | TheInfini AI Admin</title>

    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/favicon_io/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon_io/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon_io/favicon-16x16.png">
    <link rel="icon" type="image/x-icon" href="/assets/favicon_io/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Error Page Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #0a0a0a;
            color: #ffffff;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 40px 20px;
        }

        .error-icon {
            font-size: 80px;
            color: #fcd469;
            margin-bottom: 24px;
        }

        .error-title {
            font-size: 32px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 16px;
        }

        .error-message {
            font-size: 18px;
            color: #b3b3b3;
            margin-bottom: 32px;
            line-height: 1.5;
        }

        .error-details {
            background-color: #1a1a1a;
            border: 1px solid #333333;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 32px;
            text-align: left;
        }

        .error-details h4 {
            color: #fcd469;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .error-details pre {
            color: #b3b3b3;
            font-size: 14px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .error-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #fcd469;
            color: #0a0a0a;
        }

        .btn-primary:hover {
            background-color: #f5c842;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: transparent;
            color: #b3b3b3;
            border: 2px solid #333333;
        }

        .btn-secondary:hover {
            border-color: #fcd469;
            color: #fcd469;
        }

        .error-status {
            display: inline-block;
            background-color: #1a1a1a;
            border: 1px solid #333333;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 600;
            color: #fcd469;
            margin-bottom: 16px;
        }

        @media (max-width: 768px) {
            .error-container {
                padding: 20px 16px;
            }

            .error-icon {
                font-size: 60px;
            }

            .error-title {
                font-size: 24px;
            }

            .error-message {
                font-size: 16px;
            }

            .error-actions {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <% if (typeof error !== 'undefined' && error.status) { %>
            <div class="error-status">Error <%= error.status %></div>
        <% } %>
        
        <div class="error-icon">
            <% if (typeof error !== 'undefined' && error.status === 404) { %>
                <i class="fas fa-search"></i>
            <% } else if (typeof error !== 'undefined' && error.status === 403) { %>
                <i class="fas fa-lock"></i>
            <% } else if (typeof error !== 'undefined' && error.status === 401) { %>
                <i class="fas fa-user-lock"></i>
            <% } else { %>
                <i class="fas fa-exclamation-triangle"></i>
            <% } %>
        </div>

        <h1 class="error-title"><%= title %></h1>
        
        <p class="error-message"><%= message %></p>

        <% if (typeof error !== 'undefined' && error.stack && process.env.NODE_ENV !== 'production') { %>
            <div class="error-details">
                <h4>Error Details (Development Mode)</h4>
                <pre><%= error.stack %></pre>
            </div>
        <% } %>

        <div class="error-actions">
            <% if (typeof error !== 'undefined' && (error.status === 403 || error.status === 401)) { %>
                <a href="/admin/auth/login" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    Login
                </a>
                <button onclick="history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Go Back
                </button>
            <% } else { %>
                <a href="/admin/dashboard" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Go to Dashboard
                </a>
                <button onclick="history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Go Back
                </button>
            <% } %>
        </div>
    </div>

    <script>
        // Auto-refresh for server errors in development
        <% if (typeof error !== 'undefined' && error.status >= 500 && process.env.NODE_ENV === 'development') { %>
            setTimeout(() => {
                if (confirm('Would you like to retry the request?')) {
                    window.location.reload();
                }
            }, 5000);
        <% } %>
    </script>
</body>
</html>
