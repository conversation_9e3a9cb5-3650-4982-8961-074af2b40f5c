const express = require('express');
const router = express.Router();

// Import controllers
const adminController = require('../controllers/adminController');

// Import authentication middleware
const { authenticateToken, requireSuperAdmin } = require('../middleware/auth');

// Redirect /admin/login to /admin/auth/login (before authentication middleware)
router.get('/login', (req, res) => {
  res.redirect('/admin/auth/login');
});

// Apply authentication and SUPER_ADMIN role check to all admin routes
router.use(authenticateToken);
router.use(requireSuperAdmin);

// Dashboard routes
router.get('/', (req, res) => {
  res.redirect('/admin/dashboard');
});

router.get('/dashboard', adminController.dashboard);

// Subscriptions routes
router.get('/subscriptions', adminController.subscriptions);
router.get('/subscriptions/plans', adminController.viewSubscriptionPlans);
router.get('/subscriptions/paid-users', adminController.viewPaidUsers);
router.get('/subscriptions/add', adminController.addSubscription);
router.get('/subscriptions/cancel', adminController.cancelSubscription);

// User Roles routes
router.get('/user-roles', adminController.userRoles);

// Sales Reports routes
router.get('/sales-reports', adminController.salesReports);

// Support routes
router.get('/support', adminController.support);
router.get('/support/tickets/:ticketId', adminController.supportTicketDetails);
router.post('/support/tickets/:ticketId/reply', adminController.addTicketReply);
router.post('/support/tickets/:ticketId/status', adminController.updateTicketStatus);

// API Routes
router.get('/api/dashboard-stats', adminController.getDashboardStatsAPI);
router.post('/api/subscription-action', adminController.subscriptionAction);
router.post('/api/update-user-role', adminController.updateUserRole);
router.post('/api/search-user', adminController.searchUser);
router.post('/api/add-subscription', adminController.addSubscriptionAPI);
router.post('/api/cancel-subscription', adminController.cancelSubscriptionAPI);
router.post('/api/cancel-addon', adminController.cancelAddonAPI);

// User Roles API Routes
router.post('/api/create-role', adminController.createRoleAPI);
router.get('/api/roles', adminController.getRolesAPI);
router.post('/api/search-user-roles', adminController.searchUserWithRolesAPI);
router.post('/api/assign-roles', adminController.assignRolesAPI);

// Sales Reports API Routes
router.get('/api/sales-reports', adminController.getSalesReportsAPI);

// Support API Routes
router.get('/api/support/stats', adminController.getSupportStatsAPI);
router.get('/api/support/tickets', adminController.getSupportTicketsAPI);
router.get('/api/support/tickets/:ticketId', adminController.getSupportTicketAPI);
router.post('/api/support/tickets/:ticketId/reply', adminController.addTicketReplyAPI);
router.post('/api/support/tickets/:ticketId/status', adminController.updateTicketStatusAPI);

// Health check route
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'theinfini_ai_internals'
  });
});

module.exports = router;
