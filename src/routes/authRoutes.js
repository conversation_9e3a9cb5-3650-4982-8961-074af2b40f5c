const express = require('express');
const router = express.Router();

// Import controllers
const authController = require('../controllers/authController');

// Import middleware
const { 
  authenticateToken, 
  redirectIfAuthenticated, 
  extractClientIP, 
  rateLimitPerIP 
} = require('../middleware/auth');

// Apply client IP extraction to all routes
router.use(extractClientIP);

// Public routes (no authentication required)

// Login page - redirect to dashboard if already authenticated
router.get('/login', 
  redirectIfAuthenticated,
  authController.showLogin
);

// Login form submission with rate limiting
router.post('/login',
  redirectIfAuthenticated,
  rateLimitPerIP(5, 15 * 60 * 1000), // 5 attempts per 15 minutes per IP
  authController.login
);

// API Routes

// Verify token (public endpoint for checking token validity)
router.post('/api/verify-token',
  rateLimitPerIP(10, 60 * 1000), // 10 requests per minute per IP
  authController.verifyToken
);

// Check if user exists (public endpoint)
router.get('/api/check-user',
  rateLimitPerIP(10, 60 * 1000), // 10 requests per minute per IP
  authController.checkUser
);

// Auth system health check (public endpoint)
router.get('/api/health',
  authController.healthCheck
);

// Protected routes (authentication required)

// Logout
router.post('/logout',
  authenticateToken,
  authController.logout
);

// Get current user profile
router.get('/api/profile',
  authenticateToken,
  authController.getProfile
);

// Refresh token
router.post('/api/refresh-token',
  authenticateToken,
  authController.refreshToken
);

// Logout via GET (for convenience links)
router.get('/logout',
  authenticateToken,
  authController.logout
);

module.exports = router;
