<div class="greeting">
    Hello {{userName}}! 👋
</div>

<div class="message">
    We wanted to update you on the status of your support ticket. Our team has made progress on your request and we've updated the ticket status accordingly.
</div>

<div class="purchase-summary">
    <div class="purchase-title">🎫 Support Ticket Status Update</div>
    
    <div class="purchase-details">
        <div class="purchase-row">
            <span class="purchase-label">Ticket ID:</span>
            <span class="purchase-value">#{{ticket.ticketId}}</span>
        </div>
        
        <div class="purchase-row">
            <span class="purchase-label">Subject:</span>
            <span class="purchase-value">{{ticket.subject}}</span>
        </div>
        
        <div class="purchase-row">
            <span class="purchase-label">New Status:</span>
            <span class="purchase-value status-{{ticket.status}}">{{ticket.statusDisplay}}</span>
        </div>
        
        <div class="purchase-row">
            <span class="purchase-label">Updated:</span>
            <span class="purchase-value">{{ticket.updatedAt}}</span>
        </div>
    </div>
</div>

<div class="message">
    <strong>What does this status mean?</strong>
    <div style="background: #f8f9fa; border-left: 4px solid #fcd469; padding: 16px; margin: 16px 0; border-radius: 4px;">
        {{#if (eq ticket.status "OPEN")}}
            <p style="margin: 0; color: #2d3748;">Your ticket is open and awaiting review by our support team. We will respond as soon as possible.</p>
        {{else if (eq ticket.status "IN_PROGRESS")}}
            <p style="margin: 0; color: #2d3748;">Our team is actively working on your request. We may reach out if we need additional information.</p>
        {{else if (eq ticket.status "RESOLVED")}}
            <p style="margin: 0; color: #2d3748;">We believe we have resolved your issue. If you're satisfied with the resolution, no further action is needed. If you need additional help, please let us know.</p>
        {{else if (eq ticket.status "CLOSED")}}
            <p style="margin: 0; color: #2d3748;">This ticket has been closed. If you need further assistance with this or a related issue, please create a new support ticket.</p>
        {{else}}
            <p style="margin: 0; color: #2d3748;">Your ticket status has been updated to: {{ticket.statusDisplay}}</p>
        {{/if}}
    </div>
</div>

<div class="message">
    <strong>Next Steps:</strong>
    <ul style="margin: 15px 0; padding-left: 20px; color: #4a5568;">
        {{#if (eq ticket.status "RESOLVED")}}
            <li>Review the resolution provided in previous communications</li>
            <li>Test the solution if applicable</li>
            <li>Reply to this email if you need further assistance</li>
            <li>The ticket will be automatically closed if no response is received within 7 days</li>
        {{else if (eq ticket.status "CLOSED")}}
            <li>Create a new support ticket if you need additional help</li>
            <li>Reference this ticket ID if the issue is related: <strong>#{{ticket.ticketId}}</strong></li>
        {{else}}
            <li>Monitor your email for updates from our support team</li>
            <li>Reply to this email if you have additional information to provide</li>
            <li>You can log into your account to view the full ticket history</li>
        {{/if}}
    </ul>
</div>

<div class="security-notice">
    <h4>📋 Questions About This Update?</h4>
    <p>
        If you have any questions about this status update or need clarification on the next steps, please don't hesitate to reach out. You can reply to this email or contact us directly.
    </p>
</div>

<div class="message">
    <strong>Contact Information:</strong><br>
    Email: <EMAIL><br>
    Business Hours: Monday - Friday, 9:00 AM - 6:00 PM (UTC)<br>
    For urgent issues, please mark your communication as "URGENT"
</div>

<div class="message">
    Thank you for choosing The Infini AI. We appreciate your patience and are committed to providing you with excellent support.
</div>

<style>
.status-OPEN { color: #3182ce; font-weight: bold; }
.status-IN_PROGRESS { color: #dd6b20; font-weight: bold; }
.status-RESOLVED { color: #38a169; font-weight: bold; }
.status-CLOSED { color: #718096; font-weight: bold; }
</style>
