/**
 * Cancel Subscription Page JavaScript
 * Handles the 3-step workflow for cancelling user subscriptions
 */

class CancelSubscriptionManager {
    constructor() {
        this.currentUser = null;
        this.selectedSubscription = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.resetForm();
    }

    bindEvents() {
        // Step 1: User search
        document.getElementById('searchUserBtn').addEventListener('click', () => this.searchUser());
        document.getElementById('userEmail').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchUser();
            }
        });

        // Step 3: Cancellation confirmation
        document.getElementById('confirmCancelBtn').addEventListener('click', () => this.cancelSubscription());
        document.getElementById('cancelBtn').addEventListener('click', () => this.goBackToStep2());

        // Modal actions
        document.getElementById('viewPaidUsersBtn').addEventListener('click', () => {
            window.location.href = '/admin/subscriptions/paid-users';
        });
        document.getElementById('cancelAnotherBtn').addEventListener('click', () => {
            this.hideModal();
            this.resetForm();
        });
    }

    resetForm() {
        // Reset all form data
        this.currentUser = null;
        this.selectedSubscription = null;

        // Clear form fields
        document.getElementById('userEmail').value = '';
        document.getElementById('cancellationReason').value = '';

        // Show only step 1
        this.showStep(1);

        // Clear dynamic content
        document.getElementById('userInfoCard').innerHTML = '';
        document.getElementById('activeSubscriptions').innerHTML = '';
        document.getElementById('cancellationDetails').innerHTML = '';
    }

    showStep(stepNumber) {
        // Hide all steps
        for (let i = 1; i <= 3; i++) {
            const step = document.getElementById(`step-${i}`);
            if (step) {
                step.style.display = 'none';
            }
        }

        // Show the requested step
        const targetStep = document.getElementById(`step-${stepNumber}`);
        if (targetStep) {
            targetStep.style.display = 'block';
        }
    }

    async searchUser() {
        const email = document.getElementById('userEmail').value.trim();
        
        if (!email) {
            alert('Please enter an email address');
            return;
        }

        const searchBtn = document.getElementById('searchUserBtn');
        this.setButtonLoading(searchBtn, true);
        this.showLoadingOverlay();

        try {
            const response = await fetch('/admin/api/search-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (data.success) {
                this.currentUser = data.data;
                this.displayUserDetails();
                this.showStep(2);
            } else {
                alert(data.message || 'User not found');
            }
        } catch (error) {
            console.error('Error searching user:', error);
            alert('Failed to search user. Please try again.');
        } finally {
            this.setButtonLoading(searchBtn, false);
            this.hideLoadingOverlay();
        }
    }

    displayUserDetails() {
        const userInfoCard = document.getElementById('userInfoCard');
        const activeSubscriptions = document.getElementById('activeSubscriptions');

        // Display user info
        userInfoCard.innerHTML = `
            <div class="user-details">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-info">
                    <h5>${this.currentUser.first_name || ''} ${this.currentUser.last_name || ''}</h5>
                    <p class="user-email">${this.currentUser.email}</p>
                    <div class="user-meta">
                        <span class="meta-item">
                            <i class="fas fa-calendar"></i>
                            Joined: ${new Date(this.currentUser.user_created_at).toLocaleDateString()}
                        </span>
                        <span class="meta-item">
                            <i class="fas fa-crown"></i>
                            Current Plan: ${this.currentUser.current_plan || 'EXPLORER'}
                        </span>
                        <span class="meta-item status-${this.currentUser.user_active ? 'active' : 'inactive'}">
                            <i class="fas fa-circle"></i>
                            ${this.currentUser.user_active ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
            </div>
        `;

        // Display active subscriptions and add-ons
        const allActiveItems = this.currentUser.all_active_items || [];

        if (allActiveItems.length > 0) {
            activeSubscriptions.innerHTML = allActiveItems.map(item => {
                const isAddon = item.item_type === 'addon';
                const itemId = isAddon ? item.addon_id : item.subscription_id;
                const status = isAddon ? item.addon_status : item.subscription_status;

                return `
                <div class="subscription-card selectable" data-item-id="${itemId}" data-item-type="${item.item_type}">
                    <div class="subscription-header">
                        <div class="plan-type-badge ${item.plan_type.toLowerCase()}">
                            ${item.plan_type}
                            ${isAddon ? ' (Add-on)' : ''}
                        </div>
                        <h6 class="plan-name">${item.plan_name}</h6>
                    </div>
                    <div class="subscription-details">
                        <div class="detail-item">
                            <span class="label">Status:</span>
                            <span class="value status-${status.toLowerCase()}">${status}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">Start Date:</span>
                            <span class="value">${new Date(item.start_date).toLocaleDateString()}</span>
                        </div>
                        ${item.end_date ? `
                        <div class="detail-item">
                            <span class="label">End Date:</span>
                            <span class="value">${new Date(item.end_date).toLocaleDateString()}</span>
                        </div>
                        ` : ''}
                        ${isAddon ? `
                        <div class="detail-item">
                            <span class="label">Remaining Credits:</span>
                            <span class="value">${item.remaining_credits || 0}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">Remaining Projects:</span>
                            <span class="value">${item.remaining_projects || 0}</span>
                        </div>
                        ` : ''}
                    </div>
                    <div class="subscription-actions">
                        <button type="button" class="btn btn-danger btn-sm select-subscription-btn">
                            <i class="fas fa-times-circle"></i> Cancel This ${isAddon ? 'Add-on' : 'Subscription'}
                        </button>
                    </div>
                </div>
                `;
            }).join('');

            // Bind click events to subscription/addon cards
            document.querySelectorAll('.select-subscription-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const card = e.target.closest('.subscription-card');
                    const itemId = card.dataset.itemId;
                    const itemType = card.dataset.itemType;
                    this.selectItem(itemId, itemType);
                });
            });
        } else {
            activeSubscriptions.innerHTML = `
                <div class="no-subscriptions">
                    <i class="fas fa-info-circle"></i>
                    <p>This user has no active subscriptions or add-ons to cancel.</p>
                    <p>Only users with active paid subscriptions or add-ons can be cancelled.</p>
                </div>
            `;
        }
    }

    selectItem(itemId, itemType) {
        // Find the selected item (subscription or add-on)
        const allActiveItems = this.currentUser.all_active_items || [];
        this.selectedItem = allActiveItems.find(item => {
            if (itemType === 'addon') {
                return item.addon_id === itemId;
            } else {
                return item.subscription_id === itemId;
            }
        });

        if (!this.selectedItem) {
            alert('Item not found');
            return;
        }

        // Store item type for later use
        this.selectedItemType = itemType;

        this.displayCancellationReview();
        this.showStep(3);
    }

    displayCancellationReview() {
        const cancellationDetails = document.getElementById('cancellationDetails');
        const isAddon = this.selectedItemType === 'addon';
        const itemName = isAddon ? 'Add-on' : 'Subscription';
        const status = isAddon ? this.selectedItem.addon_status : this.selectedItem.subscription_status;

        cancellationDetails.innerHTML = `
            <div class="review-card">
                <h6>${itemName} to Cancel</h6>
                <div class="review-item">
                    <span class="label">User:</span>
                    <span class="value">${this.currentUser.email}</span>
                </div>
                <div class="review-item">
                    <span class="label">Plan:</span>
                    <span class="value">${this.selectedItem.plan_name} (${this.selectedItem.plan_type}${isAddon ? ' Add-on' : ''})</span>
                </div>
                <div class="review-item">
                    <span class="label">Status:</span>
                    <span class="value status-${status.toLowerCase()}">${status}</span>
                </div>
                <div class="review-item">
                    <span class="label">Start Date:</span>
                    <span class="value">${new Date(this.selectedItem.start_date).toLocaleDateString()}</span>
                </div>
                ${this.selectedItem.end_date ? `
                <div class="review-item">
                    <span class="label">End Date:</span>
                    <span class="value">${new Date(this.selectedItem.end_date).toLocaleDateString()}</span>
                </div>
                ` : ''}
                ${isAddon ? `
                <div class="review-item">
                    <span class="label">Remaining Credits:</span>
                    <span class="value">${this.selectedItem.remaining_credits || 0}</span>
                </div>
                <div class="review-item">
                    <span class="label">Remaining Projects:</span>
                    <span class="value">${this.selectedItem.remaining_projects || 0}</span>
                </div>
                ` : ''}
            </div>
            <div class="warning-card">
                <i class="fas fa-exclamation-triangle"></i>
                <div class="warning-content">
                    <h6>Important Notice</h6>
                    <ul>
                        ${isAddon ? `
                        <li>This action will immediately cancel the user's add-on pack</li>
                        <li>All remaining credits and resources will be removed</li>
                        <li>This action cannot be undone</li>
                        ` : `
                        <li>This action will immediately cancel the user's subscription</li>
                        <li>The user will be downgraded to the free EXPLORER plan</li>
                        <li>Their credits will be reset to free plan limits (30 credits)</li>
                        <li>This action cannot be undone</li>
                        `}
                    </ul>
                </div>
            </div>
        `;
    }

    goBackToStep2() {
        this.selectedItem = null;
        this.selectedItemType = null;
        this.showStep(2);
    }

    async cancelSubscription() {
        if (!this.selectedItem) {
            alert('No item selected');
            return;
        }

        const reason = document.getElementById('cancellationReason').value.trim();
        const isAddon = this.selectedItemType === 'addon';

        const confirmBtn = document.getElementById('confirmCancelBtn');
        this.setButtonLoading(confirmBtn, true);
        this.showLoadingOverlay();

        try {
            const endpoint = isAddon ? '/admin/api/cancel-addon' : '/admin/api/cancel-subscription';
            const itemId = isAddon ? this.selectedItem.addon_id : this.selectedItem.subscription_id;
            const bodyKey = isAddon ? 'addonId' : 'subscriptionId';

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    [bodyKey]: itemId,
                    reason: reason || 'Admin cancellation'
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccessModal(data.data);
            } else {
                alert(data.message || `Failed to cancel ${isAddon ? 'add-on' : 'subscription'}`);
            }
        } catch (error) {
            console.error(`Error cancelling ${isAddon ? 'add-on' : 'subscription'}:`, error);
            alert(`Failed to cancel ${isAddon ? 'add-on' : 'subscription'}. Please try again.`);
        } finally {
            this.setButtonLoading(confirmBtn, false);
            this.hideLoadingOverlay();
        }
    }

    showSuccessModal(cancellationData) {
        const successDetails = document.getElementById('successDetails');
        
        successDetails.innerHTML = `
            <div class="success-info">
                <div class="success-item">
                    <span class="label">User Email:</span>
                    <span class="value">${cancellationData.userEmail}</span>
                </div>
                <div class="success-item">
                    <span class="label">Cancelled Plan:</span>
                    <span class="value">${cancellationData.planName} (${cancellationData.planType})</span>
                </div>
                <div class="success-item">
                    <span class="label">Cancelled At:</span>
                    <span class="value">${new Date(cancellationData.cancelledAt).toLocaleString()}</span>
                </div>
                <div class="success-item">
                    <span class="label">Downgraded To:</span>
                    <span class="value">${cancellationData.downgradedTo} (Free Plan)</span>
                </div>
                ${cancellationData.cancellationReason ? `
                <div class="success-item">
                    <span class="label">Reason:</span>
                    <span class="value">${cancellationData.cancellationReason}</span>
                </div>
                ` : ''}
            </div>
        `;

        document.getElementById('successModal').style.display = 'flex';
    }

    hideModal() {
        document.getElementById('successModal').style.display = 'none';
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        } else {
            button.disabled = false;
            // Restore original content based on button ID
            if (button.id === 'searchUserBtn') {
                button.innerHTML = '<i class="fas fa-search"></i> Search User';
            } else if (button.id === 'confirmCancelBtn') {
                button.innerHTML = '<i class="fas fa-times-circle"></i> Cancel Subscription';
            }
        }
    }

    showLoadingOverlay() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    hideLoadingOverlay() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }
}

// Initialize the cancel subscription manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new CancelSubscriptionManager();
});
