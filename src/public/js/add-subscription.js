/**
 * Add Subscription Page JavaScript
 * Handles the 3-step workflow for adding subscriptions to users
 */

class AddSubscriptionManager {
    constructor() {
        this.currentUser = null;
        this.selectedPlan = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.resetForm();
    }

    bindEvents() {
        // Step 1: User search
        document.getElementById('searchUserBtn').addEventListener('click', () => this.searchUser());
        document.getElementById('userEmail').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchUser();
            }
        });

        // Step 2: Plan selection
        document.querySelectorAll('.plan-card.selectable').forEach(card => {
            card.addEventListener('click', () => this.selectPlan(card));
        });

        // Step 3: Review and confirm
        document.getElementById('addSubscriptionBtn').addEventListener('click', () => this.addSubscription());
        document.getElementById('cancelBtn').addEventListener('click', () => this.resetForm());

        // Modal actions
        document.getElementById('viewSubscriptionsBtn').addEventListener('click', () => {
            window.location.href = '/admin/subscriptions/paid-users';
        });
        document.getElementById('addAnotherBtn').addEventListener('click', () => {
            this.hideModal();
            this.resetForm();
        });
    }

    async searchUser() {
        const email = document.getElementById('userEmail').value.trim();
        const errorDiv = document.getElementById('userSearchError');
        const searchBtn = document.getElementById('searchUserBtn');

        // Validate email
        if (!email) {
            this.showError(errorDiv, 'Please enter an email address');
            return;
        }

        if (!this.isValidEmail(email)) {
            this.showError(errorDiv, 'Please enter a valid email address');
            return;
        }

        // Show loading
        this.setButtonLoading(searchBtn, true);
        this.hideError(errorDiv);

        try {
            const response = await fetch('/admin/api/search-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (data.success) {
                this.currentUser = data.data;
                this.displayUserDetails(data.data);
                this.showStep(2);
            } else {
                this.showError(errorDiv, data.message || 'User not found');
            }
        } catch (error) {
            console.error('Error searching user:', error);
            this.showError(errorDiv, 'Failed to search user. Please try again.');
        } finally {
            this.setButtonLoading(searchBtn, false);
        }
    }

    displayUserDetails(user) {
        const userDetails = document.getElementById('userDetails');
        const userAvatar = document.getElementById('userAvatar');
        const userInitials = document.getElementById('userInitials');
        const userName = document.getElementById('userName');
        const userEmailDisplay = document.getElementById('userEmailDisplay');
        const userStatus = document.getElementById('userStatus');
        const userPlan = document.getElementById('userPlan');
        const currentSubscription = document.getElementById('currentSubscription');
        const noSubscription = document.getElementById('noSubscription');
        const subscriptionDetails = document.getElementById('subscriptionDetails');

        // Display user info
        const fullName = user.first_name && user.last_name 
            ? `${user.first_name} ${user.last_name}` 
            : user.email.split('@')[0];
        
        userName.textContent = fullName;
        userEmailDisplay.textContent = user.email;

        // User avatar or initials
        if (user.profile_picture) {
            userAvatar.src = user.profile_picture;
            userAvatar.style.display = 'block';
            userInitials.style.display = 'none';
        } else {
            const initials = this.getInitials(fullName);
            userInitials.textContent = initials;
            userInitials.style.display = 'flex';
            userAvatar.style.display = 'none';
        }

        // User status
        userStatus.textContent = user.user_active ? 'Active' : 'Inactive';
        userStatus.className = `status-badge ${user.user_active ? 'active' : 'inactive'}`;

        // Current plan
        userPlan.textContent = user.current_plan || 'EXPLORER';
        userPlan.className = `plan-badge ${(user.current_plan || 'explorer').toLowerCase()}`;

        // Subscription details
        if (user.active_subscriptions && user.active_subscriptions.length > 0) {
            const subscriptionList = user.active_subscriptions.map(sub => {
                const endDate = sub.end_date ? new Date(sub.end_date).toLocaleDateString() : 'No expiry';
                return `${sub.plan_name} (${sub.plan_type}) - Active until ${endDate}`;
            }).join('<br>');

            subscriptionDetails.innerHTML = subscriptionList;
            currentSubscription.style.display = 'block';
            noSubscription.style.display = 'none';
        } else {
            currentSubscription.style.display = 'none';
            noSubscription.style.display = 'block';
        }

        userDetails.style.display = 'block';

        // Update plan cards based on user's current subscriptions
        this.updatePlanCards(user);
    }

    updatePlanCards(user) {
        // Get user's current active paid plan types (exclude ADDON)
        const currentPaidPlanTypes = [];

        // Check all active subscriptions
        if (user.active_subscriptions && user.active_subscriptions.length > 0) {
            user.active_subscriptions.forEach(subscription => {
                if (subscription.plan_type && subscription.plan_type !== 'ADDON') {
                    currentPaidPlanTypes.push(subscription.plan_type);
                }
            });
        }

        // Update all plan cards
        document.querySelectorAll('.plan-card.selectable').forEach(card => {
            const planType = card.dataset.planType;

            // Reset card state
            card.classList.remove('disabled', 'current-plan');
            card.style.pointerEvents = 'auto';

            // If this plan type matches user's current paid plan, disable it
            if (currentPaidPlanTypes.includes(planType)) {
                card.classList.add('disabled', 'current-plan');
                card.style.pointerEvents = 'none';

                // Add current plan indicator
                this.addCurrentPlanIndicator(card);
            } else {
                // Remove current plan indicator if it exists
                this.removeCurrentPlanIndicator(card);
            }
        });
    }

    derivePlanTypeFromName(planName) {
        // Derive plan type from plan name
        const name = planName.toLowerCase();
        if (name.includes('creator')) return 'CREATOR';
        if (name.includes('pro')) return 'PRO';
        if (name.includes('explorer')) return 'EXPLORER';
        if (name.includes('addon') || name.includes('add pack')) return 'ADDON';
        return null;
    }

    addCurrentPlanIndicator(card) {
        // Remove existing indicator
        this.removeCurrentPlanIndicator(card);

        // Add current plan indicator
        const indicator = document.createElement('div');
        indicator.className = 'current-plan-indicator';
        indicator.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>Current Plan</span>
        `;
        card.appendChild(indicator);
    }

    removeCurrentPlanIndicator(card) {
        const existingIndicator = card.querySelector('.current-plan-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }
    }

    selectPlan(planCard) {
        // Check if plan is disabled
        if (planCard.classList.contains('disabled')) {
            return; // Don't allow selection of disabled plans
        }

        // Remove selection from all plans
        document.querySelectorAll('.plan-card.selectable').forEach(card => {
            card.classList.remove('selected');
        });

        // Select the clicked plan
        planCard.classList.add('selected');

        // Store selected plan data
        this.selectedPlan = {
            id: planCard.dataset.planId,
            type: planCard.dataset.planType,
            name: planCard.querySelector('.plan-name').textContent,
            price: planCard.querySelector('.amount').textContent,
            currency: planCard.querySelector('.currency').textContent,
            cycle: planCard.querySelector('.cycle').textContent,
            credits: planCard.querySelector('.credits').textContent
        };

        // Show step 3 after a short delay
        setTimeout(() => {
            this.populateReview();
            this.showStep(3);
        }, 300);
    }

    populateReview() {
        if (!this.currentUser || !this.selectedPlan) return;

        const fullName = this.currentUser.first_name && this.currentUser.last_name 
            ? `${this.currentUser.first_name} ${this.currentUser.last_name}` 
            : this.currentUser.email.split('@')[0];

        document.getElementById('reviewUserName').textContent = fullName;
        document.getElementById('reviewUserEmail').textContent = this.currentUser.email;
        document.getElementById('reviewPlanName').textContent = this.selectedPlan.name;
        document.getElementById('reviewPlanType').textContent = this.selectedPlan.type;
        document.getElementById('reviewPlanPrice').textContent = `${this.selectedPlan.currency} ${this.selectedPlan.price}`;
        document.getElementById('reviewBillingCycle').textContent = this.selectedPlan.cycle.replace('/', '').trim();
        document.getElementById('reviewCredits').textContent = this.selectedPlan.credits;
    }

    async addSubscription() {
        if (!this.currentUser || !this.selectedPlan) {
            alert('Please complete all steps before adding subscription');
            return;
        }

        const addBtn = document.getElementById('addSubscriptionBtn');
        this.setButtonLoading(addBtn, true);
        this.showLoadingOverlay();

        try {
            const response = await fetch('/admin/api/add-subscription', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userId: this.currentUser.user_id,
                    planId: this.selectedPlan.id
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccessModal(data.data);
            } else {
                alert(data.message || 'Failed to add subscription');
            }
        } catch (error) {
            console.error('Error adding subscription:', error);
            alert('Failed to add subscription. Please try again.');
        } finally {
            this.setButtonLoading(addBtn, false);
            this.hideLoadingOverlay();
        }
    }

    showSuccessModal(subscriptionData) {
        const modal = document.getElementById('successModal');
        const successDetails = document.getElementById('successDetails');

        successDetails.innerHTML = `
            <div class="success-item">
                <strong>User:</strong> ${this.currentUser.email}
            </div>
            <div class="success-item">
                <strong>Plan:</strong> ${subscriptionData.planName} (${subscriptionData.planType})
            </div>
            <div class="success-item">
                <strong>Status:</strong> ${subscriptionData.status}
            </div>
            <div class="success-item">
                <strong>Start Date:</strong> ${new Date(subscriptionData.startDate).toLocaleDateString()}
            </div>
            <div class="success-item">
                <strong>End Date:</strong> ${new Date(subscriptionData.endDate).toLocaleDateString()}
            </div>
        `;

        modal.style.display = 'flex';
    }

    hideModal() {
        document.getElementById('successModal').style.display = 'none';
    }

    showStep(stepNumber) {
        // Hide all steps
        document.querySelectorAll('.subscription-step').forEach(step => {
            step.style.display = 'none';
        });

        // Show the requested step
        document.getElementById(`step-${stepNumber}`).style.display = 'block';
    }

    resetForm() {
        this.currentUser = null;
        this.selectedPlan = null;

        // Reset form fields
        document.getElementById('userEmail').value = '';
        document.getElementById('userDetails').style.display = 'none';
        
        // Remove plan selections
        document.querySelectorAll('.plan-card.selectable').forEach(card => {
            card.classList.remove('selected');
        });

        // Hide errors
        this.hideError(document.getElementById('userSearchError'));

        // Show only step 1
        this.showStep(1);
    }

    // Utility methods
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    getInitials(name) {
        return name.split(' ').map(word => word.charAt(0).toUpperCase()).join('').substring(0, 2);
    }

    showError(element, message) {
        element.textContent = message;
        element.style.display = 'block';
    }

    hideError(element) {
        element.style.display = 'none';
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        } else {
            button.disabled = false;
            button.innerHTML = button.dataset.originalText || button.innerHTML.replace('<i class="fas fa-spinner fa-spin"></i> Loading...', button.textContent);
        }
    }

    showLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('show');
        }
    }

    hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AddSubscriptionManager();
});
