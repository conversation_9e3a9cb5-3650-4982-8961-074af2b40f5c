// User Roles Management JavaScript

// Global variables
let currentUser = null;
let availableRoles = [];
let selectedRoles = [];

// Initialize user roles functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Initializing user roles management...');

    initializeEventListeners();
    loadRolesOverview();
    loadAvailableRoles();
    updateDashboardCounts();
});

// Initialize event listeners
function initializeEventListeners() {
    // Main action buttons
    const createRoleBtn = document.getElementById('createRoleBtn');
    if (createRoleBtn) {
        createRoleBtn.addEventListener('click', showCreateRoleModal);
    }

    const assignRolesBtn = document.getElementById('assignRolesBtn');
    if (assignRolesBtn) {
        assignRolesBtn.addEventListener('click', showAssignRolesModal);
    }

    const viewRolesBtn = document.getElementById('viewRolesBtn');
    if (viewRolesBtn) {
        viewRolesBtn.addEventListener('click', showAllRolesModal);
    }

    // Create role modal
    const createRoleForm = document.getElementById('createRoleForm');
    if (createRoleForm) {
        createRoleForm.addEventListener('submit', handleCreateRole);
    }

    const createRoleModalClose = document.getElementById('createRoleModalClose');
    if (createRoleModalClose) {
        createRoleModalClose.addEventListener('click', hideCreateRoleModal);
    }

    const createRoleCancelBtn = document.getElementById('createRoleCancelBtn');
    if (createRoleCancelBtn) {
        createRoleCancelBtn.addEventListener('click', hideCreateRoleModal);
    }

    // Assign roles modal
    const assignRolesModalClose = document.getElementById('assignRolesModalClose');
    if (assignRolesModalClose) {
        assignRolesModalClose.addEventListener('click', hideAssignRolesModal);
    }

    const searchUserBtn = document.getElementById('searchUserBtn');
    if (searchUserBtn) {
        searchUserBtn.addEventListener('click', handleSearchUser);
    }

    const userEmailInput = document.getElementById('userEmail');
    if (userEmailInput) {
        userEmailInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleSearchUser();
            }
        });
    }

    const assignRolesSubmitBtn = document.getElementById('assignRolesSubmitBtn');
    if (assignRolesSubmitBtn) {
        assignRolesSubmitBtn.addEventListener('click', handleAssignRoles);
    }

    const resetUserSearchBtn = document.getElementById('resetUserSearchBtn');
    if (resetUserSearchBtn) {
        resetUserSearchBtn.addEventListener('click', resetUserSearch);
    }

    // All roles modal
    const allRolesModalClose = document.getElementById('allRolesModalClose');
    if (allRolesModalClose) {
        allRolesModalClose.addEventListener('click', hideAllRolesModal);
    }

    // Close modals when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            if (e.target.id === 'createRoleModal') hideCreateRoleModal();
            if (e.target.id === 'assignRolesModal') hideAssignRolesModal();
            if (e.target.id === 'allRolesModal') hideAllRolesModal();
        }
    });
}

// Handle create role form submission
async function handleCreateRole(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const roleName = formData.get('roleName').trim();
    const roleDescription = formData.get('roleDescription').trim();
    
    if (!roleName) {
        showNotification('Role name is required', 'error');
        return;
    }
    
    showLoading(true);
    
    try {
        const response = await fetch('/admin/api/create-role', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: roleName,
                description: roleDescription,
                permissions: {}
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('Role created successfully!', 'success');
            hideCreateRoleModal();
            loadRolesOverview();
            loadAvailableRoles();
            updateDashboardCounts();
        } else {
            showNotification(result.message || 'Failed to create role', 'error');
        }
    } catch (error) {
        console.error('Error creating role:', error);
        showNotification('Failed to create role. Please try again.', 'error');
    } finally {
        showLoading(false);
    }
}

// Handle search user
async function handleSearchUser() {
    const email = document.getElementById('userEmail').value.trim();
    
    if (!email) {
        showNotification('Please enter a user email', 'error');
        return;
    }
    
    showLoading(true);
    
    try {
        const response = await fetch('/admin/api/search-user-roles', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email })
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentUser = result.data;
            displayUserDetails(currentUser);
            displayUserRoles(currentUser);
        } else {
            showNotification(result.message || 'User not found', 'error');
            hideUserDetails();
        }
    } catch (error) {
        console.error('Error searching user:', error);
        showNotification('Failed to search user. Please try again.', 'error');
        hideUserDetails();
    } finally {
        showLoading(false);
    }
}

// Display user details
function displayUserDetails(user) {
    const userDetailsSection = document.getElementById('userDetailsSection');
    const userAvatar = document.getElementById('userAvatar');
    const userName = document.getElementById('userName');
    const userEmailDisplay = document.getElementById('userEmailDisplay');
    const userPlan = document.getElementById('userPlan');
    const userStatus = document.getElementById('userStatus');
    
    // Set user avatar
    const initials = getUserInitials(user.first_name, user.last_name, user.email);
    userAvatar.textContent = initials;
    
    // Set user details
    const fullName = [user.first_name, user.last_name].filter(Boolean).join(' ') || 'No Name';
    userName.textContent = fullName;
    userEmailDisplay.textContent = user.email;
    userPlan.textContent = user.plan || 'EXPLORER';
    userStatus.textContent = user.is_active ? 'Active' : 'Inactive';
    userStatus.className = `user-status ${user.is_active ? 'active' : 'inactive'}`;
    
    userDetailsSection.style.display = 'block';
}

// Display user roles with checkboxes
function displayUserRoles(user) {
    const rolesContainer = document.getElementById('rolesContainer');
    const assignRolesBtn = document.getElementById('assignRolesSubmitBtn');
    
    if (!rolesContainer || availableRoles.length === 0) {
        rolesContainer.innerHTML = '<p>No roles available</p>';
        return;
    }
    
    // Get user's current role IDs
    const userRoleIds = user.roles ? user.roles.map(role => role.id) : [];
    selectedRoles = [...userRoleIds]; // Initialize selected roles
    
    let rolesHTML = '<div class="roles-grid">';
    
    availableRoles.forEach(role => {
        const isChecked = userRoleIds.includes(role.id);
        rolesHTML += `
            <div class="role-item">
                <label class="role-checkbox">
                    <input type="checkbox"
                           value="${role.id}"
                           ${isChecked ? 'checked' : ''}
                           data-role-id="${role.id}">
                    <span class="checkmark"></span>
                    <div class="role-info">
                        <h5>${role.name}</h5>
                        <p>${role.description || 'No description'}</p>
                    </div>
                </label>
            </div>
        `;
    });
    
    rolesHTML += '</div>';
    rolesContainer.innerHTML = rolesHTML;

    // Add event listeners to checkboxes
    const checkboxes = rolesContainer.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            handleRoleSelection(this.dataset.roleId, this.checked);
        });
    });

    // Enable assign button
    assignRolesBtn.disabled = false;
}

// Handle role selection
function handleRoleSelection(roleId, isChecked) {
    if (isChecked) {
        if (!selectedRoles.includes(roleId)) {
            selectedRoles.push(roleId);
        }
    } else {
        selectedRoles = selectedRoles.filter(id => id !== roleId);
    }
    
    console.log('Selected roles:', selectedRoles);
}

// Handle assign roles
async function handleAssignRoles() {
    if (!currentUser) {
        showNotification('No user selected', 'error');
        return;
    }
    
    showLoading(true);
    
    try {
        const response = await fetch('/admin/api/assign-roles', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                userId: currentUser.id,
                roleIds: selectedRoles
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('Roles assigned successfully!', 'success');
            // Refresh user details to show updated roles
            handleSearchUser();
        } else {
            showNotification(result.message || 'Failed to assign roles', 'error');
        }
    } catch (error) {
        console.error('Error assigning roles:', error);
        showNotification('Failed to assign roles. Please try again.', 'error');
    } finally {
        showLoading(false);
    }
}

// Load available roles
async function loadAvailableRoles() {
    try {
        const response = await fetch('/admin/api/roles');
        const result = await response.json();
        
        if (result.success) {
            availableRoles = result.data;
            console.log('Available roles loaded:', availableRoles.length);
        } else {
            console.error('Failed to load roles:', result.message);
        }
    } catch (error) {
        console.error('Error loading roles:', error);
    }
}

// Load roles overview
async function loadRolesOverview() {
    const rolesOverview = document.getElementById('rolesOverview');
    
    try {
        const response = await fetch('/admin/api/roles');
        const result = await response.json();
        
        if (result.success && result.data.length > 0) {
            let overviewHTML = '<div class="roles-overview-grid">';
            
            result.data.forEach(role => {
                overviewHTML += `
                    <div class="role-overview-card">
                        <h4>${role.name}</h4>
                        <p>${role.description || 'No description'}</p>
                        <div class="role-meta">
                            <span class="role-date">Created: ${formatDate(role.created_at)}</span>
                        </div>
                    </div>
                `;
            });
            
            overviewHTML += '</div>';
            rolesOverview.innerHTML = overviewHTML;
        } else {
            rolesOverview.innerHTML = '<p class="empty-state">No roles created yet.</p>';
        }
    } catch (error) {
        console.error('Error loading roles overview:', error);
        rolesOverview.innerHTML = '<p class="error-state">Failed to load roles.</p>';
    }
}

// Utility functions
function getUserInitials(firstName, lastName, email) {
    if (firstName && lastName) {
        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
    } else if (firstName) {
        return firstName.charAt(0).toUpperCase();
    } else if (email) {
        return email.charAt(0).toUpperCase();
    }
    return 'U';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function hideUserDetails() {
    const userDetailsSection = document.getElementById('userDetailsSection');
    userDetailsSection.style.display = 'none';
    currentUser = null;
    selectedRoles = [];
}

function resetUserSearch() {
    document.getElementById('userEmail').value = '';
    hideUserDetails();
}

function showLoading(show) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = show ? 'flex' : 'none';
    }
}

// Modal functions
function showCreateRoleModal() {
    document.getElementById('createRoleModal').style.display = 'flex';
}

function hideCreateRoleModal() {
    document.getElementById('createRoleModal').style.display = 'none';
    document.getElementById('createRoleForm').reset();
}

function showAssignRolesModal() {
    document.getElementById('assignRolesModal').style.display = 'flex';
    loadAvailableRoles(); // Ensure roles are loaded
}

function hideAssignRolesModal() {
    document.getElementById('assignRolesModal').style.display = 'none';
    resetUserSearch();
}

function showAllRolesModal() {
    document.getElementById('allRolesModal').style.display = 'flex';
    loadRolesOverview();
}

function hideAllRolesModal() {
    document.getElementById('allRolesModal').style.display = 'none';
}

// Update dashboard counts
async function updateDashboardCounts() {
    try {
        const response = await fetch('/admin/api/roles');
        const result = await response.json();

        if (result.success) {
            const roles = result.data;
            document.getElementById('totalRolesCount').textContent = roles.length;
            document.getElementById('activeRolesCount').textContent = roles.filter(r => r.is_active).length;

            // TODO: Get actual count of users with roles
            // For now, we'll leave it as 0 or implement a separate API call
        }
    } catch (error) {
        console.error('Error updating dashboard counts:', error);
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add event listener to close button
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', function() {
        notification.remove();
    });

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
