const { AuthService } = require('../services/AuthService');
const { logger } = require('../config/logger');
const crypto = require('crypto');
const environment = require('../config/environment');

/**
 * Authentication Controller
 * Handles user authentication for admin dashboard
 */
class AuthController {
  /**
   * Show login page
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async showLogin(req, res) {
    try {
      logger.info('📋 Loading admin login page');

      // Generate CSRF token
      const csrfToken = crypto.randomBytes(32).toString('hex');
      req.session.csrfToken = csrfToken;

      res.render('auth/login', {
        title: 'Admin Login - TheInfini AI',
        csrfToken: csrfToken,
        error: req.session.error || null,
        success: req.session.success || null
      });

      // Clear flash messages
      delete req.session.error;
      delete req.session.success;

      logger.info('✅ Login page loaded successfully');
    } catch (error) {
      logger.error('❌ Error loading login page:', error);
      res.status(500).render('error', {
        title: 'Login Error',
        message: 'Failed to load login page',
        error: error
      });
    }
  }

  /**
   * Handle login form submission
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async login(req, res) {
    try {
      const { identifier, password, csrfToken } = req.body;
      
      logger.info(`🔐 Login attempt from IP: ${req.clientIP} for identifier: ${identifier}`);

      // Validate CSRF token
      if (!csrfToken || csrfToken !== req.session.csrfToken) {
        logger.warn('❌ CSRF token validation failed');
        req.session.error = 'Invalid request. Please try again.';
        return res.redirect('/admin/auth/login');
      }

      // Validate input
      if (!identifier || !password) {
        logger.warn('❌ Missing login credentials');
        req.session.error = 'Email/mobile and password are required';
        return res.redirect('/admin/auth/login');
      }

      // Validate identifier format
      const validation = AuthService.validateIdentifier(identifier);
      if (!validation.isValid) {
        logger.warn(`❌ Invalid identifier format: ${identifier}`);
        req.session.error = validation.message;
        return res.redirect('/admin/auth/login');
      }

      // Attempt login
      const result = await AuthService.loginWithPassword({
        identifier: validation.identifier,
        password: password
      });

      // Store token in session
      req.session.authToken = result.token;
      req.session.user = result.user;

      // Clear CSRF token after successful login
      delete req.session.csrfToken;

      logger.info(`✅ Admin login successful for user: ${result.user.id} (${result.user.email})`);

      // Redirect to dashboard
      res.redirect('/admin/dashboard');

    } catch (error) {
      logger.error('❌ Login error:', error);
      req.session.error = error.message || 'Login failed. Please try again.';
      res.redirect('/admin/auth/login');
    }
  }

  /**
   * Handle logout
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async logout(req, res) {
    try {
      const userId = req.user?.userId;
      
      logger.info(`🚪 Logout request from user: ${userId}`);

      // Destroy session
      req.session.destroy((err) => {
        if (err) {
          logger.error('❌ Error destroying session:', err);
          return res.status(500).render('error', {
            title: 'Logout Error',
            message: 'Failed to logout properly',
            error: err
          });
        }

        logger.info(`✅ User logged out successfully: ${userId}`);
        
        // Clear cookie
        res.clearCookie('connect.sid');
        
        // Redirect to login with success message
        res.redirect('/admin/login?message=logged_out');
      });

    } catch (error) {
      logger.error('❌ Logout error:', error);
      res.status(500).render('error', {
        title: 'Logout Error',
        message: 'Failed to logout',
        error: error
      });
    }
  }

  /**
   * API endpoint to verify token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async verifyToken(req, res) {
    try {
      const { token } = req.body;

      if (!token) {
        return res.status(400).json({
          success: false,
          message: 'Token is required'
        });
      }

      const result = await AuthService.verifyToken(token);

      res.json({
        success: true,
        message: 'Token is valid',
        data: result
      });

      logger.debug(`✅ Token verified via API for user: ${result.userId}`);
    } catch (error) {
      logger.error('❌ Token verification error:', error);
      res.status(401).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }
  }

  /**
   * API endpoint to check if user exists
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async checkUser(req, res) {
    try {
      const { identifier } = req.query;

      if (!identifier) {
        return res.status(400).json({
          success: false,
          message: 'Email or mobile is required'
        });
      }

      const user = await AuthService.checkUserExists(identifier);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        message: 'User found',
        data: {
          exists: true,
          isActive: user.isActive,
          isVerified: user.isVerified
        }
      });

      logger.debug(`✅ User check via API: ${identifier} - Found: ${!!user}`);
    } catch (error) {
      logger.error('❌ User check error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to check user'
      });
    }
  }

  /**
   * API endpoint to get current user profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getProfile(req, res) {
    try {
      const userId = req.user.userId;

      const profile = await AuthService.getUserProfile(userId);

      res.json({
        success: true,
        message: 'Profile retrieved successfully',
        data: profile
      });

      logger.debug(`✅ Profile retrieved via API for user: ${userId}`);
    } catch (error) {
      logger.error('❌ Profile retrieval error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve profile'
      });
    }
  }

  /**
   * API endpoint to refresh token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async refreshToken(req, res) {
    try {
      const currentToken = req.session.authToken;

      if (!currentToken) {
        return res.status(401).json({
          success: false,
          message: 'No token to refresh'
        });
      }

      const result = await AuthService.refreshToken(currentToken);

      // Update session with new token
      req.session.authToken = result.token;

      res.json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          token: result.token
        }
      });

      logger.info(`🔄 Token refreshed via API for user: ${result.user.id}`);
    } catch (error) {
      logger.error('❌ Token refresh error:', error);
      res.status(401).json({
        success: false,
        message: 'Failed to refresh token'
      });
    }
  }

  /**
   * Health check for auth system
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async healthCheck(req, res) {
    try {
      res.json({
        success: true,
        message: 'Auth system is healthy',
        data: {
          timestamp: new Date().toISOString(),
          jwtSecret: !!environment.JWT_SECRET,
          csrfSecret: !!environment.CSRF_SECRET,
          sessionSecret: !!environment.ADMIN_SESSION_SECRET
        }
      });
    } catch (error) {
      logger.error('❌ Auth health check error:', error);
      res.status(500).json({
        success: false,
        message: 'Auth system health check failed'
      });
    }
  }
}

module.exports = new AuthController();
