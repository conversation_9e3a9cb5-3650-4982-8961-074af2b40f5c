const adminService = require('../services/adminService');
const { logger } = require('../config/logger');
const environment = require('../config/environment');

class AdminController {
  // Dashboard page
  async dashboard(req, res) {
    try {
      logger.info('📊 Loading admin dashboard');

      // Get dashboard statistics
      const stats = await adminService.getDashboardStats();

      res.render('admin/dashboard', {
        title: 'Admin Dashboard',
        stats: stats,
        currentPage: 'dashboard'
      });

      logger.info('✅ Dashboard loaded successfully');
    } catch (error) {
      logger.error('❌ Error loading dashboard:', error);
      res.status(500).render('error', {
        title: 'Dashboard Error',
        message: 'Failed to load dashboard data',
        error: error
      });
    }
  }

  // Subscriptions page
  async subscriptions(req, res) {
    try {
      console.log('💳 Loading subscriptions page for user:', req.user ? req.user.email : 'development mode');
      
      // Get subscriptions data
      const subscriptions = await adminService.getSubscriptions();
      
      res.render('admin/subscriptions', {
        title: 'Subscriptions Management',
        subscriptions: subscriptions,
        currentPage: 'subscriptions'
      });
      
      console.log('✅ Subscriptions page loaded successfully');
    } catch (error) {
      console.error('❌ Error loading subscriptions:', error.message);
      res.status(500).render('error', {
        title: 'Subscriptions Error',
        message: 'Failed to load subscriptions data',
        error: error
      });
    }
  }

  // User Roles page
  async userRoles(req, res) {
    try {
      console.log('👥 Loading user roles page for user:', req.user ? req.user.email : 'development mode');
      
      // Get user roles data
      const userRoles = await adminService.getUserRoles();
      
      res.render('admin/user-roles', {
        title: 'User Roles Management',
        userRoles: userRoles,
        currentPage: 'user-roles'
      });
      
      console.log('✅ User roles page loaded successfully');
    } catch (error) {
      console.error('❌ Error loading user roles:', error.message);
      res.status(500).render('error', {
        title: 'User Roles Error',
        message: 'Failed to load user roles data',
        error: error
      });
    }
  }

  // Sales Reports page
  async salesReports(req, res) {
    try {
      console.log('📈 Loading sales reports page for user:', req.user ? req.user.email : 'development mode');

      const { month, startDate, endDate } = req.query;

      // Get sales reports data with optional filtering
      const salesData = await adminService.getSalesReports(month, startDate, endDate);

      res.render('admin/sales-reports', {
        title: 'Sales Reports',
        salesData: salesData,
        currentPage: 'sales-reports',
        selectedMonth: month || '',
        selectedStartDate: startDate || '',
        selectedEndDate: endDate || ''
      });

      console.log('✅ Sales reports page loaded successfully');
    } catch (error) {
      console.error('❌ Error loading sales reports:', error.message);
      res.status(500).render('error', {
        title: 'Sales Reports Error',
        message: 'Failed to load sales reports data',
        error: error
      });
    }
  }

  // View Subscription Plans page
  async viewSubscriptionPlans(req, res) {
    try {
      logger.info('📋 Loading subscription plans page');

      // Get subscription plans data
      const plans = await adminService.getSubscriptionPlans();

      res.render('admin/subscription-plans', {
        title: 'Subscription Plans',
        plans: plans,
        currentPage: 'subscriptions'
      });

      logger.info('✅ Subscription plans page loaded successfully');
    } catch (error) {
      logger.error('❌ Error loading subscription plans:', error);
      res.status(500).render('error', {
        title: 'Subscription Plans Error',
        message: 'Failed to load subscription plans data',
        error: error
      });
    }
  }

  // View Paid Users page
  async viewPaidUsers(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const search = req.query.search || '';

      logger.info(`👥 Loading paid users page - Page: ${page}, Limit: ${limit}, Search: "${search}"`);

      // Get paid users data with pagination and search
      const result = await adminService.getPaidUsers(page, limit, search);

      res.render('admin/paid-users', {
        title: 'Paid Users',
        users: result.users,
        pagination: result.pagination,
        search: search,
        currentPage: 'subscriptions'
      });

      logger.info('✅ Paid users page loaded successfully');
    } catch (error) {
      logger.error('❌ Error loading paid users:', error);
      res.status(500).render('error', {
        title: 'Paid Users Error',
        message: 'Failed to load paid users data',
        error: error
      });
    }
  }

  // Add Subscription page
  async addSubscription(req, res) {
    try {
      logger.info('➕ Loading add subscription page');

      // Get active subscription plans
      const plans = await adminService.getActiveSubscriptionPlans();

      res.render('admin/add-subscription', {
        title: 'Add Subscription',
        plans: plans,
        currentPage: 'subscriptions'
      });

      logger.info('✅ Add subscription page loaded successfully');
    } catch (error) {
      logger.error('❌ Error loading add subscription page:', error);
      res.status(500).render('error', {
        title: 'Add Subscription Error',
        message: 'Failed to load add subscription page',
        error: error
      });
    }
  }

  // Cancel Subscription page
  async cancelSubscription(req, res) {
    try {
      logger.info('❌ Loading cancel subscription page');

      res.render('admin/cancel-subscription', {
        title: 'Cancel Subscription',
        currentPage: 'subscriptions'
      });

      logger.info('✅ Cancel subscription page loaded successfully');
    } catch (error) {
      logger.error('❌ Error loading cancel subscription page:', error);
      res.status(500).render('error', {
        title: 'Cancel Subscription Error',
        message: 'Failed to load cancel subscription page',
        error: error
      });
    }
  }

  // API endpoint for dashboard stats
  async getDashboardStatsAPI(req, res) {
    try {
      console.log('📊 API: Getting dashboard stats for user:', req.user ? req.user.email : 'development mode');
      
      const stats = await adminService.getDashboardStats();
      
      res.json({
        success: true,
        data: stats
      });
      
      console.log('✅ Dashboard stats API response sent');
    } catch (error) {
      console.error('❌ Error in dashboard stats API:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch dashboard statistics',
        error: error.message
      });
    }
  }

  // API endpoint for subscription actions
  async subscriptionAction(req, res) {
    try {
      const { action, subscriptionId } = req.body;
      console.log(`💳 API: Performing subscription action '${action}' for subscription ${subscriptionId}`);
      
      const result = await adminService.performSubscriptionAction(action, subscriptionId, 'admin');
      
      res.json({
        success: true,
        message: `Subscription ${action} completed successfully`,
        data: result
      });
      
      console.log('✅ Subscription action completed successfully');
    } catch (error) {
      console.error('❌ Error in subscription action API:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to perform subscription action',
        error: error.message
      });
    }
  }

  // API endpoint for user role updates
  async updateUserRole(req, res) {
    try {
      const { userId, newRole } = req.body;
      console.log(`👥 API: Updating user ${userId} role to ${newRole}`);

      const result = await adminService.updateUserRole(userId, newRole, 'admin');

      res.json({
        success: true,
        message: 'User role updated successfully',
        data: result
      });

      console.log('✅ User role updated successfully');
    } catch (error) {
      console.error('❌ Error in update user role API:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to update user role',
        error: error.message
      });
    }
  }

  // API endpoint for searching user by email
  async searchUser(req, res) {
    try {
      const { email } = req.body;
      logger.info(`🔍 API: Searching user by email: ${email}`);

      if (!email || !email.trim()) {
        return res.status(400).json({
          success: false,
          message: 'Email is required'
        });
      }

      const user = await adminService.searchUserByEmail(email.trim());

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found with this email'
        });
      }

      res.json({
        success: true,
        message: 'User found successfully',
        data: user
      });

      logger.info(`✅ User found: ${user.email}`);
    } catch (error) {
      logger.error('❌ Error in search user API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search user',
        error: error.message
      });
    }
  }

  // API endpoint for adding subscription
  async addSubscriptionAPI(req, res) {
    try {
      const { userId, planId } = req.body;
      logger.info(`➕ API: Adding subscription for user ${userId} with plan ${planId}`);

      if (!userId || !planId) {
        return res.status(400).json({
          success: false,
          message: 'User ID and Plan ID are required'
        });
      }

      const result = await adminService.createAdminSubscription(userId, planId, 'admin');

      res.json({
        success: true,
        message: 'Subscription added successfully',
        data: result
      });

      logger.info(`✅ Subscription added successfully for user ${userId}`);
    } catch (error) {
      logger.error('❌ Error in add subscription API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add subscription',
        error: error.message
      });
    }
  }

  // API endpoint for cancelling subscription
  async cancelSubscriptionAPI(req, res) {
    try {
      const { subscriptionId, reason } = req.body;
      logger.info(`❌ API: Cancelling subscription ${subscriptionId} with reason: ${reason}`);

      if (!subscriptionId) {
        return res.status(400).json({
          success: false,
          message: 'Subscription ID is required'
        });
      }

      const result = await adminService.cancelAdminSubscription(subscriptionId, reason || 'Admin cancellation', 'admin');

      res.json({
        success: true,
        message: 'Subscription cancelled successfully',
        data: result
      });

      logger.info(`✅ Subscription cancelled successfully: ${subscriptionId}`);
    } catch (error) {
      logger.error('❌ Error in cancel subscription API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel subscription',
        error: error.message
      });
    }
  }

  // API endpoint for cancelling add-on
  async cancelAddonAPI(req, res) {
    try {
      const { addonId, reason } = req.body;
      logger.info(`❌ API: Cancelling add-on ${addonId} with reason: ${reason}`);

      if (!addonId) {
        return res.status(400).json({
          success: false,
          message: 'Add-on ID is required'
        });
      }

      const result = await adminService.cancelAdminAddon(addonId, reason || 'Admin cancellation', 'admin');

      res.json({
        success: true,
        message: 'Add-on cancelled successfully',
        data: result
      });

      logger.info(`✅ Add-on cancelled successfully: ${addonId}`);
    } catch (error) {
      logger.error('❌ Error in cancel add-on API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel add-on',
        error: error.message
      });
    }
  }

  // API endpoint for creating a new role
  async createRoleAPI(req, res) {
    try {
      const { name, description, permissions } = req.body;
      logger.info(`🔧 API: Creating new role: ${name}`);

      if (!name || !name.trim()) {
        return res.status(400).json({
          success: false,
          message: 'Role name is required'
        });
      }

      const result = await adminService.createRole(
        name.trim(),
        description || '',
        permissions || {},
        'admin'
      );

      res.json({
        success: true,
        message: 'Role created successfully',
        data: result
      });

      logger.info(`✅ Role created successfully: ${name}`);
    } catch (error) {
      logger.error('❌ Error in create role API:', error);
      res.status(500).json({
        success: false,
        message: error.message.includes('already exists') ? error.message : 'Failed to create role',
        error: error.message
      });
    }
  }

  // API endpoint for getting all roles
  async getRolesAPI(req, res) {
    try {
      logger.info('📋 API: Getting all roles');

      const roles = await adminService.getAllRoles();

      res.json({
        success: true,
        message: 'Roles retrieved successfully',
        data: roles
      });

      logger.info(`✅ Retrieved ${roles.length} roles`);
    } catch (error) {
      logger.error('❌ Error in get roles API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve roles',
        error: error.message
      });
    }
  }

  // API endpoint for searching user with their roles
  async searchUserWithRolesAPI(req, res) {
    try {
      const { email } = req.body;
      logger.info(`🔍 API: Searching user with roles by email: ${email}`);

      if (!email || !email.trim()) {
        return res.status(400).json({
          success: false,
          message: 'Email is required'
        });
      }

      const user = await adminService.searchUserWithRoles(email.trim());

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found with this email'
        });
      }

      res.json({
        success: true,
        message: 'User found successfully',
        data: user
      });

      logger.info(`✅ User found with roles: ${user.email}`);
    } catch (error) {
      logger.error('❌ Error in search user with roles API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search user',
        error: error.message
      });
    }
  }

  // API endpoint for assigning roles to user
  async assignRolesAPI(req, res) {
    try {
      const { userId, roleIds } = req.body;
      logger.info(`👥 API: Assigning roles to user ${userId}: ${roleIds?.join(', ')}`);

      if (!userId || !roleIds || !Array.isArray(roleIds)) {
        return res.status(400).json({
          success: false,
          message: 'User ID and role IDs array are required'
        });
      }

      const result = await adminService.assignRolesToUser(userId, roleIds, 'admin');

      res.json({
        success: true,
        message: 'Roles assigned successfully',
        data: result
      });

      logger.info(`✅ Roles assigned successfully to user ${userId}`);
    } catch (error) {
      logger.error('❌ Error in assign roles API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to assign roles',
        error: error.message
      });
    }
  }

  // API endpoint for sales reports
  async getSalesReportsAPI(req, res) {
    try {
      const { month, startDate, endDate } = req.query;
      logger.info('📈 API: Getting sales reports data', { month, startDate, endDate });

      const salesData = await adminService.getSalesReports(month, startDate, endDate);

      res.json({
        success: true,
        data: salesData
      });

      logger.info('✅ Sales reports API data retrieved successfully');
    } catch (error) {
      logger.error('❌ Error in sales reports API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get sales reports data',
        error: error.message
      });
    }
  }

  // Support Module Methods

  // Support page
  async support(req, res) {
    try {
      logger.info('🎫 Loading support page');

      // Get support statistics
      const stats = await adminService.getSupportStats();

      res.render('admin/support', {
        title: 'Support Management',
        stats: stats,
        currentPage: 'support'
      });

      logger.info('✅ Support page loaded successfully');
    } catch (error) {
      logger.error('❌ Error loading support page:', error);
      res.status(500).render('error', {
        title: 'Support Error',
        message: 'Failed to load support data',
        error: error
      });
    }
  }

  // Support ticket details page
  async supportTicketDetails(req, res) {
    try {
      const { ticketId } = req.params;
      logger.info(`🎫 Loading support ticket details for ID: ${ticketId}`);

      const ticket = await adminService.getSupportTicketById(ticketId);

      if (!ticket) {
        return res.status(404).render('error', {
          title: 'Ticket Not Found',
          message: 'The requested support ticket could not be found.',
          error: { status: 404 }
        });
      }
      res.render('admin/support-ticket-details', {
        title: `Support Ticket - ${ticket.subject}`,
        ticket: ticket,
        currentPage: 'support'
      });

      logger.info(`✅ Support ticket details loaded for: ${ticket.subject}`);
    } catch (error) {
      logger.error('❌ Error loading support ticket details:', error);
      res.status(500).render('error', {
        title: 'Support Ticket Error',
        message: 'Failed to load support ticket details',
        error: error
      });
    }
  }

  // Add ticket reply (form submission)
  async addTicketReply(req, res) {
    try {
      const { ticketId } = req.params;
      const { replyMessage } = req.body;

      logger.info(`🎫 Adding reply to ticket ${ticketId}`);

      if (!replyMessage || replyMessage.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Reply message is required'
        });
      }

      const result = await adminService.addTicketReply(ticketId, replyMessage.trim(), 'admin');

      // Redirect back to ticket details page
      res.redirect(`/admin/support/tickets/${ticketId}?success=reply_added`);
    } catch (error) {
      logger.error('❌ Error adding ticket reply:', error);
      res.redirect(`/admin/support/tickets/${req.params.ticketId}?error=reply_failed`);
    }
  }

  // Update ticket status (form submission)
  async updateTicketStatus(req, res) {
    try {
      const { ticketId } = req.params;
      const { status } = req.body;

      logger.info(`🎫 Updating ticket ${ticketId} status to ${status}`);

      if (!status) {
        return res.status(400).json({
          success: false,
          message: 'Status is required'
        });
      }

      const result = await adminService.updateTicketStatus(ticketId, status, 'admin');

      // Redirect back to ticket details page
      res.redirect(`/admin/support/tickets/${ticketId}?success=status_updated`);
    } catch (error) {
      logger.error('❌ Error updating ticket status:', error);
      res.redirect(`/admin/support/tickets/${req.params.ticketId}?error=status_update_failed`);
    }
  }

  // API Methods for Support

  // API endpoint for support statistics
  async getSupportStatsAPI(req, res) {
    try {
      logger.info('🎫 API: Support statistics requested');

      const stats = await adminService.getSupportStats();

      res.json({
        success: true,
        message: 'Support statistics retrieved successfully',
        data: stats
      });

      logger.info('✅ Support statistics API response sent');
    } catch (error) {
      logger.error('❌ Error in support statistics API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve support statistics',
        error: error.message
      });
    }
  }

  // API endpoint for support tickets list
  async getSupportTicketsAPI(req, res) {
    try {
      const { page = 1, limit = 20, status, priority } = req.query;
      logger.info(`🎫 API: Support tickets requested - Page: ${page}, Status: ${status || 'all'}`);

      const result = await adminService.getSupportTickets({
        page: parseInt(page),
        limit: parseInt(limit),
        status,
        priority
      });

      res.json({
        success: true,
        message: 'Support tickets retrieved successfully',
        data: result
      });

      logger.info(`✅ Support tickets API response sent (${result.tickets.length} tickets)`);
    } catch (error) {
      logger.error('❌ Error in support tickets API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve support tickets',
        error: error.message
      });
    }
  }

  // API endpoint for specific support ticket
  async getSupportTicketAPI(req, res) {
    try {
      const { ticketId } = req.params;
      logger.info(`🎫 API: Support ticket details requested for ID: ${ticketId}`);

      const ticket = await adminService.getSupportTicketById(ticketId);

      if (!ticket) {
        return res.status(404).json({
          success: false,
          message: 'Support ticket not found'
        });
      }


      res.json({
        success: true,
        message: 'Support ticket retrieved successfully',
        data: { ticket }
      });

      logger.info(`✅ Support ticket API response sent for: ${ticket.subject}`);
    } catch (error) {
      logger.error('❌ Error in support ticket API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve support ticket',
        error: error.message
      });
    }
  }

  // API endpoint for adding ticket reply
  async addTicketReplyAPI(req, res) {
    try {
      const { ticketId } = req.params;
      const { replyMessage } = req.body;

      logger.info(`🎫 API: Adding reply to ticket ${ticketId}`);

      if (!replyMessage || replyMessage.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Reply message is required'
        });
      }

      const result = await adminService.addTicketReply(ticketId, replyMessage.trim(), 'admin');

      res.json({
        success: true,
        message: 'Reply added successfully',
        data: result
      });

      logger.info(`✅ Ticket reply API response sent for ticket ${ticketId}`);
    } catch (error) {
      logger.error('❌ Error in ticket reply API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add reply',
        error: error.message
      });
    }
  }

  // API endpoint for updating ticket status
  async updateTicketStatusAPI(req, res) {
    try {
      const { ticketId } = req.params;
      const { status } = req.body;

      logger.info(`🎫 API: Updating ticket ${ticketId} status to ${status}`);

      if (!status) {
        return res.status(400).json({
          success: false,
          message: 'Status is required'
        });
      }

      const validStatuses = ['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
        });
      }

      const result = await adminService.updateTicketStatus(ticketId, status, 'admin');

      res.json({
        success: true,
        message: 'Ticket status updated successfully',
        data: result
      });

      logger.info(`✅ Ticket status update API response sent for ticket ${ticketId}`);
    } catch (error) {
      logger.error('❌ Error in ticket status update API:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update ticket status',
        error: error.message
      });
    }
  }
}

module.exports = new AdminController();
