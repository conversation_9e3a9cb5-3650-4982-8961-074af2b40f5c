require('dotenv').config();

const environment = {
  // Server Configuration
  PORT: process.env.PORT || 5500,
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // JWT Configuration
  JWT_SECRET: process.env.JWT_SECRET,
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '24h',
  
  // User Database Configuration
  USER_DB: {
    HOST: process.env.USER_DB_HOST || 'localhost',
    PORT: process.env.USER_DB_PORT || 3306,
    NAME: process.env.USER_DB_NAME || 'infini_ai_users',
    USERNAME: process.env.USER_DB_USERNAME || 'inf_ai_user',
    PASSWORD: process.env.USER_DB_PASSWORD || 'inf_ai_user'
  },
  
  // Chat Database Configuration
  CHAT_DB: {
    HOST: process.env.CHAT_DB_HOST || 'localhost',
    PORT: process.env.CHAT_DB_PORT || 3306,
    NAME: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
    USERNAME: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
    PASSWORD: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs'
  },
  
  // Security Configuration
  CSRF_SECRET: process.env.CSRF_SECRET,
  ADMIN_SESSION_SECRET: process.env.ADMIN_SESSION_SECRET,
  
  // Frontend URL for CORS
  FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:3000',
  
  // Logging
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  
  // Development flags
  isDevelopment: () => environment.NODE_ENV === 'development',
  isProduction: () => environment.NODE_ENV === 'production',
  isTest: () => environment.NODE_ENV === 'test',

  MAIN_APP_URL: process.env.MAIN_APP_URL || 'http://localhost:5529/api',
};

// Validate required environment variables
const requiredVars = [
  'JWT_SECRET',
  'USER_DB_HOST',
  'USER_DB_NAME',
  'USER_DB_USERNAME',
  'USER_DB_PASSWORD',
  'CHAT_DB_HOST',
  'CHAT_DB_NAME',
  'CHAT_DB_USERNAME',
  'CHAT_DB_PASSWORD',
  'ADMIN_SESSION_SECRET'
];

const missingVars = requiredVars.filter(varName => {
  const keys = varName.split('_');
  let obj = environment;
  
  for (const key of keys) {
    if (key === 'DB') {
      // Handle nested DB objects
      continue;
    }
    if (obj[key] === undefined || obj[key] === '') {
      return true;
    }
    if (typeof obj[key] === 'object') {
      obj = obj[key];
    }
  }
  return false;
});

if (missingVars.length > 0 && !environment.isTest() && !environment.isDevelopment()) {
  console.error('❌ Missing required environment variables:', missingVars);
  console.error('Please check your .env file and ensure all required variables are set.');
  process.exit(1);
} else if (missingVars.length > 0 && environment.isDevelopment()) {
  console.warn('⚠️ Missing environment variables in development mode:', missingVars);
  console.warn('Some features may not work properly without proper configuration.');
}

console.log('✅ Environment configuration loaded successfully');
console.log(`🌍 Running in ${environment.NODE_ENV} mode`);

module.exports = environment;
