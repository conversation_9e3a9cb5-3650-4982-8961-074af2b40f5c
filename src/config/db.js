const mysql = require('mysql2/promise');
const environment = require('./environment');
const { logger } = require('./logger');

// Database connection pools
let userDbPool = null;
let chatDbPool = null;

// User Database Connection Pool
const createUserDbPool = () => {
  console.log('🔗 Initializing User Database connection pool...');
  
  userDbPool = mysql.createPool({
    host: environment.USER_DB.HOST,
    port: environment.USER_DB.PORT,
    user: environment.USER_DB.USERNAME,
    password: environment.USER_DB.PASSWORD,
    database: environment.USER_DB.NAME,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    charset: 'utf8mb4'
  });

  console.log(`✅ User Database pool created for ${environment.USER_DB.NAME}`);
  return userDbPool;
};

// Chat Database Connection Pool
const createChatDbPool = () => {
  console.log('🔗 Initializing Chat Database connection pool...');
  
  chatDbPool = mysql.createPool({
    host: environment.CHAT_DB.HOST,
    port: environment.CHAT_DB.PORT,
    user: environment.CHAT_DB.USERNAME,
    password: environment.CHAT_DB.PASSWORD,
    database: environment.CHAT_DB.NAME,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    charset: 'utf8mb4'
  });

  console.log(`✅ Chat Database pool created for ${environment.CHAT_DB.NAME}`);
  return chatDbPool;
};

// Get User Database connection
const getUserDb = () => {
  if (!userDbPool) {
    userDbPool = createUserDbPool();
  }
  return userDbPool;
};

// Get Chat Database connection
const getChatDb = () => {
  if (!chatDbPool) {
    chatDbPool = createChatDbPool();
  }
  return chatDbPool;
};

// Test database connections
const testConnections = async () => {
  console.log('🧪 Testing database connections...');

  try {
    // Skip database tests in development if credentials are missing
    if (environment.isDevelopment() &&
        (!environment.USER_DB.HOST || !environment.USER_DB.USERNAME)) {
      console.warn('⚠️ Skipping database connection test in development mode (missing credentials)');
      return false;
    }

    // Test User Database
    const userDb = getUserDb();
    const [userRows] = await userDb.execute('SELECT 1 as test');
    console.log('✅ User Database connection successful');

    // Test Chat Database
    const chatDb = getChatDb();
    const [chatRows] = await chatDb.execute('SELECT 1 as test');
    console.log('✅ Chat Database connection successful');

    console.log('🎉 All database connections established successfully');
    return true;
  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
    if (environment.isDevelopment()) {
      console.warn('⚠️ Continuing in development mode without database connection');
      return false;
    }
    return false;
  }
};

// Test database connection for health checks
const testDatabaseConnection = async () => {
  try {
    const results = {
      userDb: { status: 'unknown', message: '' },
      chatDb: { status: 'unknown', message: '' },
      overall: 'unknown'
    };

    // Skip database tests in development if credentials are missing
    if (environment.isDevelopment() &&
        (!environment.USER_DB.HOST || !environment.USER_DB.USERNAME)) {
      results.userDb = { status: 'skipped', message: 'Missing credentials in development' };
      results.chatDb = { status: 'skipped', message: 'Missing credentials in development' };
      results.overall = 'skipped';
      return results;
    }

    // Test User Database
    try {
      const userDb = getUserDb();
      const [userRows] = await userDb.execute('SELECT 1 as test');
      results.userDb = { status: 'healthy', message: 'Connection successful' };
    } catch (userError) {
      results.userDb = { status: 'error', message: userError.message };
    }

    // Test Chat Database
    try {
      const chatDb = getChatDb();
      const [chatRows] = await chatDb.execute('SELECT 1 as test');
      results.chatDb = { status: 'healthy', message: 'Connection successful' };
    } catch (chatError) {
      results.chatDb = { status: 'error', message: chatError.message };
    }

    // Determine overall status
    if (results.userDb.status === 'healthy' && results.chatDb.status === 'healthy') {
      results.overall = 'healthy';
    } else if (results.userDb.status === 'skipped' && results.chatDb.status === 'skipped') {
      results.overall = 'skipped';
    } else if (results.userDb.status === 'error' || results.chatDb.status === 'error') {
      results.overall = 'error';
    } else {
      results.overall = 'degraded';
    }

    return results;
  } catch (error) {
    return {
      userDb: { status: 'error', message: error.message },
      chatDb: { status: 'error', message: error.message },
      overall: 'error'
    };
  }
};

// Close all database connections
const closeConnections = async () => {
  console.log('🔌 Closing database connections...');
  
  try {
    if (userDbPool) {
      await userDbPool.end();
      console.log('✅ User Database connection pool closed');
    }
    
    if (chatDbPool) {
      await chatDbPool.end();
      console.log('✅ Chat Database connection pool closed');
    }
    
    console.log('🎉 All database connections closed successfully');
  } catch (error) {
    console.error('❌ Error closing database connections:', error.message);
  }
};

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, closing database connections...');
  await closeConnections();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, closing database connections...');
  await closeConnections();
  process.exit(0);
});

// Initialize database connections
const initializeDatabase = async () => {
  try {
    logger.info('🔗 Initializing database connections...');

    // Create connection pools
    createUserDbPool();
    createChatDbPool();

    // Test connections
    const isConnected = await testConnections();

    if (isConnected) {
      logger.info('✅ Database initialization completed successfully');
    } else {
      logger.warn('⚠️ Database initialization completed with warnings');
    }

    return isConnected;
  } catch (error) {
    logger.error('❌ Database initialization failed:', error);
    throw error;
  }
};

module.exports = {
  getUserDb,
  getChatDb,
  testConnections,
  testDatabaseConnection,
  closeConnections,
  initializeDatabase
};
