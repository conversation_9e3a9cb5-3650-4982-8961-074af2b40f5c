const express = require('express');
const path = require('path');
const helmet = require('helmet');
const cors = require('cors');
const session = require('express-session');
const cookieParser = require('cookie-parser');
require('dotenv').config();

// Import logger
const { logger, requestLogger } = require('./config/logger');
const environment = require('./config/environment');

// Import services
const { EmailService } = require('./services/EmailService');

// Import middleware
const { setUserLocals } = require('./middleware/auth');

// Import routes
const adminRoutes = require('./routes/adminRoutes');
const authRoutes = require('./routes/authRoutes');

const app = express();
const PORT = process.env.PORT || 5500;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdnjs.cloudflare.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

// Request logging middleware
app.use(requestLogger);

// Cookie parser middleware
app.use(cookieParser());

// Session middleware for authentication
app.use(session({
  secret: environment.ADMIN_SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: environment.isProduction(), // Use secure cookies in production
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  },
  name: 'admin.sid' // Custom session name
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Make user data available to all views
app.use(setUserLocals);

// Public Health Check API (no authentication required)
app.get('/health', async (req, res) => {
  try {
    logger.info('🏥 Health check requested');

    // Basic health check response
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'theinfini_ai_internals',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      }
    };

    // Test database connectivity
    try {
      const { testDatabaseConnection } = require('./config/db');
      const dbStatus = await testDatabaseConnection();
      healthData.database = dbStatus;
    } catch (dbError) {
      logger.warn('⚠️ Database health check failed:', dbError.message);
      healthData.database = {
        status: 'error',
        message: dbError.message
      };
      healthData.status = 'degraded';
    }

    // Test email service
    try {
      const { EmailService } = require('./services/EmailService');
      const emailStatus = EmailService.getStatus();
      healthData.email = {
        status: emailStatus.initialized ? 'healthy' : 'inactive',
        templatesLoaded: emailStatus.templatesLoaded
      };
    } catch (emailError) {
      logger.warn('⚠️ Email service health check failed:', emailError.message);
      healthData.email = {
        status: 'error',
        message: emailError.message
      };
    }

    const statusCode = healthData.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(healthData);

    logger.info(`✅ Health check completed - Status: ${healthData.status}`);
  } catch (error) {
    logger.error('❌ Health check failed:', error);
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'theinfini_ai_internals',
      message: 'Health check failed',
      error: error.message
    });
  }
});

// API Health Check (alternative endpoint)
app.get('/api/health', async (req, res) => {
  try {
    logger.info('🏥 API Health check requested');

    const healthData = {
      success: true,
      message: 'API is healthy',
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'theinfini_ai_internals',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      }
    };

    res.json(healthData);
    logger.info('✅ API Health check completed successfully');
  } catch (error) {
    logger.error('❌ API Health check failed:', error);
    res.status(500).json({
      success: false,
      message: 'API health check failed',
      error: error.message
    });
  }
});

// Routes
app.use('/admin/auth', authRoutes);
app.use('/admin', adminRoutes);

// Root redirect to admin login
app.get('/', (req, res) => {
  res.redirect('/admin/auth/login');
});

// 404 handler
app.use((req, res) => {
  logger.warn(`404 - Page not found: ${req.method} ${req.originalUrl} - IP: ${req.ip}`);
  res.status(404).render('error', {
    title: 'Page Not Found',
    message: 'The page you are looking for does not exist.',
    error: { status: 404 }
  });
});

// Error handler
app.use((err, req, res, next) => {
  logger.error(`Error in ${req.method} ${req.originalUrl}:`, err);

  const status = err.status || 500;
  const message = process.env.NODE_ENV === 'production'
    ? 'Something went wrong!'
    : err.message;

  res.status(status).render('error', {
    title: 'Error',
    message: message,
    error: process.env.NODE_ENV === 'production' ? {} : err
  });
});

// Initialize services and start server
async function startServer() {
  try {
    // Initialize database connections
    logger.info('🔗 Initializing database connections...');
    const { initializeDatabase } = require('./config/db');
    await initializeDatabase();

    // Initialize email service
    logger.info('📧 Initializing email service...');
    await EmailService.initialize();

    // Start server
    app.listen(PORT, () => {
      logger.info(`🚀 TheInfini AI Internals Admin Dashboard running on port ${PORT}`);
      logger.info(`📊 Dashboard URL: http://localhost:${PORT}/admin/dashboard`);
      logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`📁 Static files served from: ${path.join(__dirname, 'public')}`);
      logger.info(`👁️ Views directory: ${path.join(__dirname, 'views')}`);

      // Log email service status
      const emailStatus = EmailService.getStatus();
      logger.info(`📧 Email service status: ${emailStatus.initialized ? 'Active' : 'Inactive'} (${emailStatus.templatesLoaded} templates loaded)`);
    });
  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the application
startServer();

module.exports = app;
