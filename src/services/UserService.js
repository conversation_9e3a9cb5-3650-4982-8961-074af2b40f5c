const { logger } = require('../config/logger');
const { getUserDb } = require('../config/db');

/**
 * User Service Class
 * Handles user data retrieval from database
 */
class UserService {
  /**
   * Get user details by user ID
   * @param {number} userId - User ID
   * @returns {Promise<Object|null>} User details or null if not found
   */
  static async getUserById(userId) {
    try {
      const db = getUserDb();
      
      const [users] = await db.execute(`
        SELECT 
          u.id,
          u.email,
          u.mobile,
          u.is_active,
          u.is_verified,
          u.created_at,
          up.first_name,
          up.last_name,
          up.profile_picture,
          up.plan
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE u.id = ?
      `, [userId]);

      if (users.length === 0) {
        logger.warn(`User not found with ID: ${userId}`);
        return null;
      }

      const user = users[0];
      
      // Format user data
      const userData = {
        id: user.id,
        email: user.email,
        mobile: user.mobile,
        isActive: user.is_active,
        isVerified: user.is_verified,
        createdAt: user.created_at,
        firstName: user.first_name,
        lastName: user.last_name,
        profilePicture: user.profile_picture,
        plan: user.plan,
        name: user.first_name ? `${user.first_name} ${user.last_name || ''}`.trim() : user.email.split('@')[0]
      };

      logger.info(`User details retrieved for ID: ${userId}`);
      return userData;
    } catch (error) {
      logger.error('Error fetching user details:', error);
      throw error;
    }
  }

  /**
   * Get user details by email
   * @param {string} email - User email
   * @returns {Promise<Object|null>} User details or null if not found
   */
  static async getUserByEmail(email) {
    try {
      const db = getUserDb();
      
      const [users] = await db.execute(`
        SELECT 
          u.id,
          u.email,
          u.mobile,
          u.is_active,
          u.is_verified,
          u.created_at,
          up.first_name,
          up.last_name,
          up.profile_picture,
          up.plan
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE u.email = ?
      `, [email]);

      if (users.length === 0) {
        logger.warn(`User not found with email: ${email}`);
        return null;
      }

      const user = users[0];
      
      // Format user data
      const userData = {
        id: user.id,
        email: user.email,
        mobile: user.mobile,
        isActive: user.is_active,
        isVerified: user.is_verified,
        createdAt: user.created_at,
        firstName: user.first_name,
        lastName: user.last_name,
        profilePicture: user.profile_picture,
        plan: user.plan,
        name: user.first_name ? `${user.first_name} ${user.last_name || ''}`.trim() : user.email.split('@')[0]
      };

      logger.info(`User details retrieved for email: ${email}`);
      return userData;
    } catch (error) {
      logger.error('Error fetching user details by email:', error);
      throw error;
    }
  }

  /**
   * Get subscription details by subscription ID
   * @param {string} subscriptionId - Subscription ID
   * @returns {Promise<Object|null>} Subscription details or null if not found
   */
  static async getSubscriptionById(subscriptionId) {
    try {
      const db = getUserDb();
      
      const [subscriptions] = await db.execute(`
        SELECT 
          us.id,
          us.user_id,
          us.plan_id,
          us.status,
          us.start_date,
          us.end_date,
          us.next_billing_date,
          us.cancelled_at,
          us.cancellation_reason,
          us.created_at,
          us.updated_at,
          sp.name as plan_name,
          sp.plan_type,
          sp.price,
          sp.currency,
          sp.billing_cycle,
          sp.credits,
          sp.is_unlimited_credits,
          sp.features,
          sp.limits
        FROM user_subscriptions us
        INNER JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.id = ?
      `, [subscriptionId]);

      if (subscriptions.length === 0) {
        logger.warn(`Subscription not found with ID: ${subscriptionId}`);
        return null;
      }

      const subscription = subscriptions[0];
      
      // Format subscription data
      const subscriptionData = {
        id: subscription.id,
        userId: subscription.user_id,
        planId: subscription.plan_id,
        status: subscription.status,
        startDate: subscription.start_date,
        endDate: subscription.end_date,
        nextBillingDate: subscription.next_billing_date,
        cancelledAt: subscription.cancelled_at,
        cancellationReason: subscription.cancellation_reason,
        createdAt: subscription.created_at,
        updatedAt: subscription.updated_at,
        plan: {
          name: subscription.plan_name,
          planType: subscription.plan_type,
          price: subscription.price,
          currency: subscription.currency,
          billingCycle: subscription.billing_cycle,
          credits: subscription.credits,
          isUnlimitedCredits: subscription.is_unlimited_credits,
          features: subscription.features,
          limits: subscription.limits
        }
      };

      logger.info(`Subscription details retrieved for ID: ${subscriptionId}`);
      return subscriptionData;
    } catch (error) {
      logger.error('Error fetching subscription details:', error);
      throw error;
    }
  }

  /**
   * Get plan details by plan ID
   * @param {number} planId - Plan ID
   * @returns {Promise<Object|null>} Plan details or null if not found
   */
  static async getPlanById(planId) {
    try {
      const db = getUserDb();
      
      const [plans] = await db.execute(`
        SELECT 
          id,
          plan_type,
          name,
          description,
          price,
          currency,
          billing_cycle,
          credits,
          is_unlimited_credits,
          features,
          limits,
          is_active,
          sort_order,
          created_at,
          updated_at
        FROM subscription_plans
        WHERE id = ?
      `, [planId]);

      if (plans.length === 0) {
        logger.warn(`Plan not found with ID: ${planId}`);
        return null;
      }

      const plan = plans[0];
      
      // Format plan data
      const planData = {
        id: plan.id,
        planType: plan.plan_type,
        name: plan.name,
        description: plan.description,
        price: plan.price,
        currency: plan.currency,
        billingCycle: plan.billing_cycle,
        credits: plan.credits,
        isUnlimitedCredits: plan.is_unlimited_credits,
        features: plan.features,
        limits: plan.limits,
        isActive: plan.is_active,
        sortOrder: plan.sort_order,
        createdAt: plan.created_at,
        updatedAt: plan.updated_at
      };

      logger.info(`Plan details retrieved for ID: ${planId}`);
      return planData;
    } catch (error) {
      logger.error('Error fetching plan details:', error);
      throw error;
    }
  }

  /**
   * Get user with subscription and plan details
   * @param {number} userId - User ID
   * @param {string} subscriptionId - Subscription ID
   * @returns {Promise<Object|null>} Combined user, subscription and plan details
   */
  static async getUserWithSubscription(userId, subscriptionId) {
    try {
      const [user, subscription] = await Promise.all([
        this.getUserById(userId),
        this.getSubscriptionById(subscriptionId)
      ]);

      if (!user || !subscription) {
        logger.warn(`User or subscription not found - User ID: ${userId}, Subscription ID: ${subscriptionId}`);
        return null;
      }

      return {
        user,
        subscription,
        plan: subscription.plan
      };
    } catch (error) {
      logger.error('Error fetching user with subscription details:', error);
      throw error;
    }
  }
}

module.exports = { UserService };
