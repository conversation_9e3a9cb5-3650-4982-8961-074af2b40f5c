const nodemailer = require('nodemailer');
const handlebars = require('handlebars');
const fs = require('fs').promises;
const path = require('path');
const { logger } = require('../config/logger');

/**
 * Email Service Class
 * Handles all email sending functionality with beautiful templates
 */
class EmailService {
  static transporter = null;
  static templates = new Map();

  /**
   * Initialize email service with SMTP configuration
   */
  static async initialize() {
    try {
      // Check if SMTP credentials are configured
      if (!process.env.SMTP_PASSWORD || process.env.SMTP_PASSWORD === 'your_app_password_here') {
        logger.warn('SMTP password not configured or using placeholder. Email service will be initialized in test mode.');
        this.transporter = null;
        await this.loadTemplates();
        logger.info('Email service initialized in test mode (no SMTP credentials)');
        return;
      }

      // Create SMTP transporter
      const smtpPort = parseInt(process.env.SMTP_PORT || '465');
      const isSecure = process.env.SMTP_SECURE === 'true' || smtpPort === 465;

      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST || 'smtpout.secureserver.net',
        port: smtpPort,
        secure: isSecure, // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER || '<EMAIL>',
          pass: process.env.SMTP_PASSWORD,
        },
        tls: {
          // Do not fail on invalid certs for development
          rejectUnauthorized: process.env.NODE_ENV === 'production',
          // Force TLS version for better compatibility
          minVersion: 'TLSv1.2',
        },
        // Enable STARTTLS for port 587
        requireTLS: !isSecure,
        // Improved connection settings for GoDaddy SMTP
        connectionTimeout: 120000, // 2 minutes
        greetingTimeout: 60000, // 1 minute
        socketTimeout: 120000, // 2 minutes
        // Connection pooling for better performance
        pool: true,
        maxConnections: 5,
        maxMessages: 100,
        // Keep connections alive
        keepAlive: true,
        // Additional options for reliability
        debug: process.env.NODE_ENV === 'development',
        logger: process.env.NODE_ENV === 'development',
      });

      // Verify SMTP connection with timeout and fallback
      logger.info('Verifying SMTP connection...');
      try {
        // Add timeout to SMTP verification
        await Promise.race([
          this.verifyConnection(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('SMTP verification timeout')), 30000)
          )
        ]);
      } catch (verifyError) {
        logger.warn(`SMTP verification failed: ${verifyError.message}`);

        // In development, continue without SMTP but log the issue
        if (process.env.NODE_ENV === 'development') {
          logger.warn('Continuing in development mode without SMTP verification');
          logger.warn('Emails will be logged but may not be sent');
        } else {
          throw verifyError; // Fail in production
        }
      }

      // Load email templates
      await this.loadTemplates();

      logger.info('Email service initialized successfully');
    } catch (error) {
      logger.error('Error initializing email service:', error);
      // Don't throw error in development to allow server to start
      if (process.env.NODE_ENV === 'production') {
        throw error;
      } else {
        logger.warn('Email service initialization failed, continuing in test mode');
        this.transporter = null;
        await this.loadTemplates();
      }
    }
  }

  /**
   * Verify SMTP connection
   */
  static async verifyConnection() {
    try {
      await this.transporter.verify();
      logger.info('SMTP connection verified successfully');
    } catch (error) {
      logger.error('SMTP connection verification failed:', error);
      throw new Error('Failed to connect to SMTP server. Please check your email configuration.');
    }
  }

  /**
   * Load email templates from the templates directory
   */
  static async loadTemplates() {
    try {
      // Register Handlebars helpers
      handlebars.registerHelper('eq', function(a, b) {
        return a === b;
      });

      const templatesDir = path.join(__dirname, '../templates/email');

      // Load base template
      const baseTemplatePath = path.join(templatesDir, 'base.hbs');
      const baseTemplateContent = await fs.readFile(baseTemplatePath, 'utf-8');
      this.templates.set('base', handlebars.compile(baseTemplateContent));

      // Load purchase notification templates
      const subscriptionTemplatePath = path.join(templatesDir, 'subscription-purchase.hbs');
      const subscriptionTemplateContent = await fs.readFile(subscriptionTemplatePath, 'utf-8');
      this.templates.set('subscription-purchase', handlebars.compile(subscriptionTemplateContent));

      const subscriptionCancelledTemplatePath = path.join(templatesDir, 'subscription-cancelled.hbs');
      const subscriptionCancelledTemplateContent = await fs.readFile(subscriptionCancelledTemplatePath, 'utf-8');
      this.templates.set('subscription-cancelled', handlebars.compile(subscriptionCancelledTemplateContent));

      // Load support ticket templates
      const ticketReplyTemplatePath = path.join(templatesDir, 'ticket-reply.hbs');
      const ticketReplyTemplateContent = await fs.readFile(ticketReplyTemplatePath, 'utf-8');
      this.templates.set('ticket-reply', handlebars.compile(ticketReplyTemplateContent));

      const ticketStatusUpdateTemplatePath = path.join(templatesDir, 'ticket-status-update.hbs');
      const ticketStatusUpdateTemplateContent = await fs.readFile(ticketStatusUpdateTemplatePath, 'utf-8');
      this.templates.set('ticket-status-update', handlebars.compile(ticketStatusUpdateTemplateContent));

      logger.info(`Email templates loaded successfully (${this.templates.size} templates)`);
    } catch (error) {
      logger.error('Error loading email templates:', error);
      throw error;
    }
  }

  /**
   * Generic email sending method
   * @param {string} email - Recipient email address
   * @param {string} subject - Email subject
   * @param {string} templateName - Template name (without .hbs extension)
   * @param {Object} templateData - Data to pass to the template
   * @returns {Promise<boolean>} Success status
   */
  static async sendEmail(email, subject, templateName, templateData) {
    try {
      if (!this.transporter) {
        logger.warn(`Email service not initialized - skipping ${templateName} email to ${email}`);
        return false;
      }

      // Get the specific template and base template
      const specificTemplate = this.templates.get(templateName);
      const baseTemplate = this.templates.get('base');

      if (!specificTemplate) {
        throw new Error(`Template '${templateName}' not found`);
      }

      if (!baseTemplate) {
        throw new Error('Base template not loaded');
      }

      // Generate email content
      const specificContent = specificTemplate(templateData);
      const fullEmailContent = baseTemplate({
        subject: subject,
        body: specificContent,
      });

      // Email options
      const mailOptions = {
        from: {
          name: 'The Infini AI Support',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: subject,
        html: fullEmailContent,
        text: this.generatePlainTextFromTemplate(templateName, templateData),
      };

      const info = await this.transporter.sendMail(mailOptions);

      logger.info(`${templateName} email sent successfully to ${email}`, {
        messageId: info.messageId,
        template: templateName,
      });

      return true;
    } catch (error) {
      logger.error(`Error sending ${templateName} email to ${email}:`, error);
      return false;
    }
  }

  /**
   * Send subscription purchase confirmation email
   * @param {string} email - Recipient email address
   * @param {Object} data - Purchase data
   * @param {Object} data.user - User information
   * @param {Object} data.plan - Subscription plan
   * @param {Object} data.transaction - Payment transaction
   * @param {Object} data.subscription - User subscription
   * @param {string} data.invoiceUrl - Invoice download URL (optional)
   * @returns {Promise<boolean>} Success status
   */
  static async sendSubscriptionPurchaseEmail(email, data) {
    try {
      if (!this.transporter) {
        logger.warn('Email service not initialized - skipping subscription purchase email');
        return false;
      }

      const { user, plan, transaction, subscription, invoiceUrl } = data;

      // Prepare template data
      const templateData = {
        userName: user.name || user.email.split('@')[0],
        planName: plan.name,
        billingCycle: this.getBillingCycleText(plan.billing_cycle),
        credits: plan.credits || 'Unlimited',
        projects: plan.limits?.projects || 'Unlimited',
        filesPerDay: plan.limits?.filesPerDay || 'Unlimited',
        amount: parseFloat(transaction.amount || 0).toFixed(2),
        currency: transaction.currency || 'INR',
        nextBillingDate: subscription?.next_billing_date ?
          new Date(subscription.next_billing_date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }) : null,
        invoiceUrl,
      };

      // Generate email content
      const subscriptionTemplate = this.templates.get('subscription-purchase');
      const baseTemplate = this.templates.get('base');

      if (!subscriptionTemplate || !baseTemplate) {
        throw new Error('Email templates not loaded');
      }

      const subscriptionContent = subscriptionTemplate(templateData);
      const fullEmailContent = baseTemplate({
        subject: 'Subscription Activated - The Infini AI',
        body: subscriptionContent,
      });

      // Email options
      const mailOptions = {
        from: {
          name: 'The Infini AI Support',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: `🎉 Your ${plan.name} subscription is now active!`,
        html: fullEmailContent,
        text: this.generatePlainTextPurchase('subscription', templateData),
      };

      const info = await this.transporter.sendMail(mailOptions);

      logger.info(`Subscription purchase email sent successfully to ${email}`, {
        messageId: info.messageId,
        planName: plan.name,
      });

      return true;
    } catch (error) {
      logger.error('Error sending subscription purchase email:', error);
      return false;
    }
  }

  /**
   * Send subscription cancellation email
   * @param {string} email - Recipient email
   * @param {Object} data - Cancellation data
   * @param {Object} data.user - User information
   * @param {Object} data.plan - Cancelled plan information
   * @param {Object} data.subscription - Subscription information
   * @returns {Promise<boolean>} Success status
   */
  static async sendSubscriptionCancelledEmail(email, data) {
    try {
      if (!this.transporter) {
        logger.warn('Email transporter not initialized, skipping subscription cancellation email');
        return false;
      }

      const { user, plan, subscription } = data;

      // Prepare template data
      const templateData = {
        userName: user.name || user.email.split('@')[0],
        planName: plan.name,
        cancellationDate: new Date(subscription.cancelled_at).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        cancellationReason: subscription.cancellation_reason || null,
        accessUntil: subscription.end_date ?
          new Date(subscription.end_date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }) : null,
        dashboardUrl: `${process.env.FRONTEND_URL || 'https://theinfiniai.live'}/dashboard`,
        supportUrl: `${process.env.FRONTEND_URL || 'https://theinfiniai.live'}/support`,
      };

      // Generate email content
      const cancellationTemplate = this.templates.get('subscription-cancelled');
      const baseTemplate = this.templates.get('base');

      if (!cancellationTemplate || !baseTemplate) {
        throw new Error('Email templates not loaded');
      }

      const cancellationContent = cancellationTemplate(templateData);
      const fullEmailContent = baseTemplate({
        subject: 'Subscription Cancelled - The Infini AI',
        body: cancellationContent,
      });

      // Email options
      const mailOptions = {
        from: {
          name: 'The Infini AI Support',
          address: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
        },
        to: email,
        subject: `😔 Your ${plan.name} subscription has been cancelled`,
        html: fullEmailContent,
        text: this.generatePlainTextCancellation(templateData),
      };

      const info = await this.transporter.sendMail(mailOptions);

      logger.info(`Subscription cancellation email sent successfully to ${email}`, {
        messageId: info.messageId,
        planName: plan.name,
      });

      return true;
    } catch (error) {
      logger.error('Error sending subscription cancellation email:', error);
      return false;
    }
  }

  /**
   * Get billing cycle text
   * @param {string} billingCycle - Billing cycle
   * @returns {string} Formatted billing cycle text
   */
  static getBillingCycleText(billingCycle) {
    const cycles = {
      'WEEKLY': 'week',
      'MONTHLY': 'month',
      'YEARLY': 'year',
      'ONE_TIME': 'one-time'
    };
    return cycles[billingCycle] || 'month';
  }

  /**
   * Generate plain text version of purchase email
   * @param {string} type - Purchase type (subscription/addon)
   * @param {Object} data - Template data
   * @returns {string} Plain text email content
   */
  static generatePlainTextPurchase(type, data) {
    const isSubscription = type === 'subscription';

    return `
${isSubscription ? 'Subscription Activated!' : 'Add-on Purchase Confirmed!'}

Hello ${data.userName}!

${isSubscription ?
  `Your ${data.planName} subscription has been successfully activated.` :
  `Your ${data.planName} add-on purchase has been processed successfully.`
}

Purchase Details:
- ${isSubscription ? 'Plan' : 'Add-on'}: ${data.planName}
${data.credits ? `- Credits: ${data.credits}` : ''}
${data.projects ? `- Projects: ${data.projects}` : ''}
${data.filesPerDay ? `- Files per day: ${data.filesPerDay}` : ''}
- Amount: ${data.currency} ${data.amount}
${data.nextBillingDate ? `- Next billing: ${data.nextBillingDate}` : ''}

${isSubscription ?
  'Your subscription is now active and will automatically renew on the next billing date.' :
  'Your additional resources are now available in your account.'
}

${data.invoiceUrl ? `Download your invoice: ${data.invoiceUrl}` : ''}

Thank you for choosing The Infini AI!

Best regards,
The Infini AI Team
    `.trim();
  }

  /**
   * Generate plain text version of subscription cancellation email
   * @param {Object} data - Template data
   * @returns {string} Plain text email content
   */
  static generatePlainTextCancellation(data) {
    return `
Subscription Cancelled

Hello ${data.userName},

We're sorry to see you go! Your ${data.planName} subscription has been successfully cancelled as requested.

Cancellation Details:
- Plan: ${data.planName}
- Cancelled On: ${data.cancellationDate}${data.cancellationReason ? `
- Reason: ${data.cancellationReason}` : ''}${data.accessUntil ? `
- Access Until: ${data.accessUntil}` : ''}

What Happens Next:
• Your account has been downgraded to the Explorer Plan (free tier)
• You'll have 30 credits per week and access to basic features
• All your projects and data remain safe and accessible
• No further charges will be made to your payment method

Want to Come Back?
You can reactivate your subscription anytime from your dashboard. We'd love to have you back!

What you'll get back:
• Full access to premium features
• Higher credit limits and project quotas
• Priority support and advanced AI models
• All the tools you need to succeed

Visit your dashboard: ${data.dashboardUrl}
Contact support: ${data.supportUrl}

Feedback Welcome:
We're always looking to improve. If you have a moment, we'd love to hear about your experience and how we can serve you better in the future.

Thank you for being part of the The Infini AI community. We hope to see you again soon!

Best regards,
The The Infini AI Team
    `.trim();
  }

  /**
   * Generate plain text version from template name and data
   * @param {string} templateName - Template name
   * @param {Object} data - Template data
   * @returns {string} Plain text email content
   */
  static generatePlainTextFromTemplate(templateName, data) {
    switch (templateName) {
      case 'subscription-purchase':
        return this.generatePlainTextPurchase('subscription', data);
      case 'subscription-cancelled':
        return this.generatePlainTextCancellation(data);
      case 'ticket-status-update':
        return this.generatePlainTextTicketStatusUpdate(data);
      case 'ticket-reply':
        return this.generatePlainTextTicketReply(data);
      default:
        // Fallback to a basic plain text version
        return `
Hello ${data.userName || 'Valued Customer'},

This is a notification from The Infini AI support team.

Thank you for choosing The Infini AI.

Best regards,
The Infini AI Team
        `.trim();
    }
  }

  /**
   * Generate plain text version of ticket status update email
   * @param {Object} data - Template data
   * @returns {string} Plain text email content
   */
  static generatePlainTextTicketStatusUpdate(data) {
    const { ticket } = data;
    return `
Support Ticket Status Update

Hello ${data.userName},

We wanted to update you on the status of your support ticket.

Ticket Details:
- Ticket ID: #${ticket.ticketId}
- Subject: ${ticket.subject}
- New Status: ${ticket.statusDisplay}
- Updated: ${ticket.updatedAt}

What does this status mean?
${ticket.status === 'OPEN' ? 'Your ticket is open and awaiting review by our support team. We will respond as soon as possible.' :
  ticket.status === 'IN_PROGRESS' ? 'Our team is actively working on your request. We may reach out if we need additional information.' :
  ticket.status === 'RESOLVED' ? 'We believe we have resolved your issue. If you\'re satisfied with the resolution, no further action is needed.' :
  ticket.status === 'CLOSED' ? 'This ticket has been closed. If you need further assistance, please create a new support ticket.' :
  `Your ticket status has been updated to: ${ticket.statusDisplay}`}

Contact Information:
Email: <EMAIL>
Business Hours: Monday - Friday, 9:00 AM - 6:00 PM (UTC)

Thank you for choosing The Infini AI.

Best regards,
The Infini AI Team
    `.trim();
  }

  /**
   * Generate plain text version of ticket reply email
   * @param {Object} data - Template data
   * @returns {string} Plain text email content
   */
  static generatePlainTextTicketReply(data) {
    const { ticket } = data;
    return `
New Reply to Your Support Ticket

Hello ${data.userName},

We have a new reply to your support ticket.

Ticket Details:
- Ticket ID: #${ticket.ticketId}
- Subject: ${ticket.subject}
- Reply Date: ${ticket.repliedAt}

Our Reply:
${ticket.replyMessage}

What's next?
• If this resolves your issue, no further action is needed
• If you need additional help, you can reply to this email
• You can also log into your account to view the full ticket history
• Reference your ticket ID: #${ticket.ticketId} in any future communications

Contact Information:
Email: <EMAIL>
Business Hours: Monday - Friday, 9:00 AM - 6:00 PM (UTC)

Thank you for choosing The Infini AI.

Best regards,
The Infini AI Team
    `.trim();
  }

  /**
   * Get email service status
   * @returns {Object} Service status information
   */
  static getStatus() {
    return {
      initialized: !!this.transporter,
      templatesLoaded: this.templates.size,
      smtpHost: process.env.SMTP_HOST || 'smtpout.secureserver.net',
      smtpPort: process.env.SMTP_PORT || '465',
      fromEmail: process.env.SMTP_FROM_EMAIL || '<EMAIL>',
    };
  }
}

module.exports = { EmailService };
