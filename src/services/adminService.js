const { logger } = require('../config/logger');
const { getUserDb } = require('../config/db');
const { EmailService } = require('./EmailService');
const { UserService } = require('./UserService');
const { v4: uuidv4 } = require('uuid');

class AdminService {
  // Get dashboard statistics
  async getDashboardStats() {
    logger.info('📊 Dashboard statistics requested');

    try {
      const db = getUserDb();

      // Get total users count
      const [totalUsersResult] = await db.execute(`
        SELECT COUNT(*) as count FROM users
      `);

      // Get active subscriptions count
      const [activeSubscriptionsResult] = await db.execute(`
        SELECT COUNT(*) as count FROM user_subscriptions WHERE status = 'ACTIVE'
      `);

      // Get new users this month count
      const [newUsersThisMonthResult] = await db.execute(`
        SELECT COUNT(*) as count
        FROM users
        WHERE YEAR(created_at) = YEAR(CURDATE())
        AND MONTH(created_at) = MONTH(CURDATE())
      `);

      // Calculate revenue this month from active subscriptions and add-ons
      const [subscriptionRevenueResult] = await db.execute(`
        SELECT COALESCE(SUM(sp.price), 0) as revenue
        FROM user_subscriptions us
        INNER JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'ACTIVE'
        AND YEAR(us.created_at) = YEAR(CURDATE())
        AND MONTH(us.created_at) = MONTH(CURDATE())
      `);

      const [addonRevenueResult] = await db.execute(`
        SELECT COALESCE(SUM(sp.price), 0) as revenue
        FROM user_addons ua
        INNER JOIN subscription_plans sp ON ua.plan_id = sp.id
        WHERE ua.is_active = 1
        AND YEAR(ua.created_at) = YEAR(CURDATE())
        AND MONTH(ua.created_at) = MONTH(CURDATE())
      `);

      // Get recent users (last 5 users)
      const [recentUsersResult] = await db.execute(`
        SELECT
          u.id,
          u.email,
          u.created_at,
          COALESCE(CONCAT(up.first_name, ' ', up.last_name), 'Unknown User') as name
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        ORDER BY u.created_at DESC
        LIMIT 5
      `);

      const totalUsers = totalUsersResult[0].count;
      const totalSubscriptions = activeSubscriptionsResult[0].count;
      const newUsersThisMonth = newUsersThisMonthResult[0].count;
      const subscriptionRevenue = parseFloat(subscriptionRevenueResult[0].revenue) || 0;
      const addonRevenue = parseFloat(addonRevenueResult[0].revenue) || 0;
      const revenueThisMonth = subscriptionRevenue + addonRevenue;
      const recentUsers = recentUsersResult;

      logger.info(`✅ Dashboard stats retrieved - Users: ${totalUsers}, Subscriptions: ${totalSubscriptions}, New Users: ${newUsersThisMonth}, Revenue: ₹${revenueThisMonth}`);

      return {
        totalUsers,
        totalSubscriptions,
        newUsersThisMonth,
        revenueThisMonth,
        recentUsers,
        lastUpdated: new Date()
      };
    } catch (error) {
      logger.error('❌ Error fetching dashboard statistics:', error);

      // Return default values if database is not available
      return {
        totalUsers: 0,
        totalSubscriptions: 0,
        newUsersThisMonth: 0,
        revenueThisMonth: 0,
        recentUsers: [],
        lastUpdated: new Date()
      };
    }
  }

  // Get subscriptions data
  async getSubscriptions() {
    logger.info('💳 Subscriptions data requested');

    try {
      const db = getUserDb();

      // Get count of active paid users
      const [activePaidUsersResult] = await db.execute(`
        SELECT COUNT(DISTINCT us.user_id) as count
        FROM user_subscriptions us
        WHERE us.status = 'ACTIVE'
      `);

      // Get count of subscription plans
      const [plansCountResult] = await db.execute(`
        SELECT COUNT(*) as count
        FROM subscription_plans
        WHERE is_active = 1
      `);

      const activePaidUsersCount = activePaidUsersResult[0].count;
      const plansCount = plansCountResult[0].count;
      logger.info(`✅ Retrieved subscription data - Active paid users: ${activePaidUsersCount}, Plans: ${plansCount}`);

      return {
        activePaidUsersCount,
        plansCount
      };
    } catch (error) {
      logger.error('❌ Error fetching subscription data:', error);
      // Return default values if database is not available
      return {
        activePaidUsersCount: 0
      };
    }
  }

  // Get all subscription plans
  async getSubscriptionPlans() {
    logger.info('📋 Subscription plans data requested');

    try {
      const db = getUserDb();
      const [plans] = await db.execute(`
        SELECT
          id,
          plan_type,
          name,
          description,
          price,
          currency,
          billing_cycle,
          credits,
          is_unlimited_credits,
          features,
          limits,
          is_active,
          sort_order,
          created_at,
          updated_at
        FROM subscription_plans
        ORDER BY sort_order ASC, created_at DESC
      `);

      logger.info(`✅ Retrieved ${plans.length} subscription plans`);
      return plans;
    } catch (error) {
      logger.error('❌ Error fetching subscription plans:', error);
      throw error;
    }
  }

  // Get paginated paid users with subscription details
  async getPaidUsers(page = 1, limit = 10, search = '') {
    logger.info(`👥 Paid users data requested - Page: ${page}, Limit: ${limit}, Search: "${search}"`);

    try {
      const db = getUserDb();
      const offset = (page - 1) * limit;

      // Build search conditions
      let searchCondition = '';
      if (search && search.trim()) {
        const searchTerm = `%${search.trim()}%`;
        searchCondition = `
          AND (
            u.email LIKE '${searchTerm}' OR
            up.first_name LIKE '${searchTerm}' OR
            up.last_name LIKE '${searchTerm}' OR
            CONCAT(up.first_name, ' ', up.last_name) LIKE '${searchTerm}'
          )
        `;
      }

      // Get total count of paid users (users with active or cancelled subscriptions)
      const [countResult] = await db.execute(`
        SELECT COUNT(DISTINCT us.user_id) as total
        FROM user_subscriptions us
        INNER JOIN users u ON us.user_id = u.id
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE us.status IN ('ACTIVE', 'CANCELLED', 'EXPIRED')
        ${searchCondition}
      `);

      const totalUsers = countResult[0].total;
      const totalPages = Math.ceil(totalUsers / limit);

      // Get paginated paid users with their subscription details
      const [users] = await db.execute(`
        SELECT
          u.id as user_id,
          u.email,
          u.mobile,
          u.is_active as user_active,
          u.is_verified,
          u.created_at as user_created_at,
          up.first_name,
          up.last_name,
          up.profile_picture,
          us.id as subscription_id,
          us.status as subscription_status,
          us.start_date,
          us.end_date,
          us.next_billing_date,
          us.cancelled_at,
          us.cancellation_reason,
          sp.name as plan_name,
          sp.plan_type,
          sp.price,
          sp.currency,
          sp.billing_cycle,
          sp.credits,
          sp.is_unlimited_credits
        FROM user_subscriptions us
        INNER JOIN users u ON us.user_id = u.id
        LEFT JOIN user_profiles up ON u.id = up.user_id
        INNER JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status IN ('ACTIVE', 'CANCELLED', 'EXPIRED')
        ${searchCondition}
        ORDER BY us.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `);

      logger.info(`✅ Retrieved ${users.length} paid users for page ${page}`);

      return {
        users,
        pagination: {
          currentPage: page,
          totalPages,
          totalUsers,
          limit,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      logger.error('❌ Error fetching paid users:', error);
      throw error;
    }
  }

  // Get user roles data
  async getUserRoles() {
    logger.info('👥 User roles data requested');

    try {
      const db = getUserDb();

      // Get all roles
      const [roles] = await db.execute(`
        SELECT
          id,
          name,
          description,
          permissions,
          is_active,
          created_at,
          updated_at
        FROM roles
        WHERE is_active = 1
        ORDER BY created_at DESC
      `);

      // Get role statistics
      const [roleStats] = await db.execute(`
        SELECT
          r.id,
          r.name,
          COUNT(ur.user_id) as user_count
        FROM roles r
        LEFT JOIN user_roles ur ON r.id = ur.role_id AND ur.is_active = 1
        WHERE r.is_active = 1
        GROUP BY r.id, r.name
        ORDER BY r.created_at DESC
      `);

      logger.info(`✅ Retrieved ${roles.length} roles and statistics`);

      return {
        roles,
        roleStats
      };
    } catch (error) {
      logger.error('❌ Error fetching user roles data:', error);
      throw error;
    }
  }

  // Get sales reports data
  async getSalesReports(month = null, startDate = null, endDate = null) {
    logger.info('📈 Sales reports data requested', { month, startDate, endDate });

    try {
      const db = getUserDb();
      let dateFilter = '';
      let params = [];

      // Determine date filtering
      if (month) {
        // Month format: YYYY-MM
        const [year, monthNum] = month.split('-');
        dateFilter = `AND YEAR(created_at) = ? AND MONTH(created_at) = ?`;
        params = [parseInt(year), parseInt(monthNum)];
        logger.info(`📅 Filtering by month: ${month}`);
      } else if (startDate && endDate) {
        // Validate date range (max 6 months)
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffMonths = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());

        if (diffMonths > 6) {
          throw new Error('Date range cannot exceed 6 months');
        }

        dateFilter = `AND DATE(created_at) BETWEEN ? AND ?`;
        params = [startDate, endDate];
        logger.info(`📅 Filtering by date range: ${startDate} to ${endDate}`);
      } else {
        // Default to current month
        const now = new Date();
        dateFilter = `AND YEAR(created_at) = ? AND MONTH(created_at) = ?`;
        params = [now.getFullYear(), now.getMonth() + 1];
        logger.info(`📅 Using default current month filter`);
      }

      // Get active subscriptions count
      const [activeSubscriptions] = await db.execute(`
        SELECT COUNT(*) as count
        FROM user_subscriptions
        WHERE status = 'ACTIVE' ${dateFilter}
      `, params);

      // Get add-ons count
      const [addOns] = await db.execute(`
        SELECT COUNT(*) as count
        FROM user_addons
        WHERE is_active = 1 ${dateFilter}
      `, params);

      // Get cancelled subscriptions count
      const [cancelledSubscriptions] = await db.execute(`
        SELECT COUNT(*) as count
        FROM user_subscriptions
        WHERE status = 'CANCELLED' ${dateFilter.replace('created_at', 'cancelled_at')}
      `, params);

      // Create specific date filters for revenue queries
      let subscriptionDateFilter = dateFilter.replace(/created_at/g, 'us.created_at');
      let addonDateFilter = dateFilter.replace(/created_at/g, 'ua.created_at');
      let cancelledDateFilter = dateFilter.replace(/created_at/g, 'us.cancelled_at');

      // Calculate revenue from active subscriptions created in the period
      const [subscriptionRevenue] = await db.execute(`
        SELECT COALESCE(SUM(sp.price), 0) as revenue
        FROM user_subscriptions us
        INNER JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'ACTIVE' ${subscriptionDateFilter}
      `, params);

      // Calculate revenue from add-ons purchased in the period
      const [addonRevenue] = await db.execute(`
        SELECT COALESCE(SUM(sp.price), 0) as revenue
        FROM user_addons ua
        INNER JOIN subscription_plans sp ON ua.plan_id = sp.id
        WHERE ua.is_active = 1 ${addonDateFilter}
      `, params);

      // Calculate revenue from cancelled subscriptions (for the period they were cancelled)
      const [cancelledRevenue] = await db.execute(`
        SELECT COALESCE(SUM(sp.price), 0) as revenue
        FROM user_subscriptions us
        INNER JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'CANCELLED' ${cancelledDateFilter}
      `, params);

      // Calculate total revenue
      const totalRevenue = parseFloat(subscriptionRevenue[0].revenue) +
                          parseFloat(addonRevenue[0].revenue) +
                          parseFloat(cancelledRevenue[0].revenue);

      // Format report period for better display
      let reportPeriod;
      if (month) {
        const [year, monthNum] = month.split('-');
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                           'July', 'August', 'September', 'October', 'November', 'December'];
        reportPeriod = `${monthNames[parseInt(monthNum) - 1]} ${year}`;
      } else if (startDate && endDate) {
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        const startMonth = startDateObj.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        const endMonth = endDateObj.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

        if (startMonth === endMonth) {
          reportPeriod = startMonth;
        } else {
          reportPeriod = `${startDateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endDateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
        }
      } else {
        // Default to current month
        const now = new Date();
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                           'July', 'August', 'September', 'October', 'November', 'December'];
        reportPeriod = `${monthNames[now.getMonth()]} ${now.getFullYear()}`;
      }

      const reportData = {
        activeSubscriptions: activeSubscriptions[0].count,
        addOns: addOns[0].count,
        cancelledSubscriptions: cancelledSubscriptions[0].count,
        revenue: {
          total: totalRevenue,
          subscriptions: parseFloat(subscriptionRevenue[0].revenue),
          addons: parseFloat(addonRevenue[0].revenue),
          cancelled: parseFloat(cancelledRevenue[0].revenue)
        },
        reportPeriod: reportPeriod,
        generatedAt: new Date()
      };

      logger.info('✅ Sales reports data retrieved successfully', {
        ...reportData,
        revenue: {
          total: `₹${totalRevenue.toFixed(2)}`,
          subscriptions: `₹${parseFloat(subscriptionRevenue[0].revenue).toFixed(2)}`,
          addons: `₹${parseFloat(addonRevenue[0].revenue).toFixed(2)}`,
          cancelled: `₹${parseFloat(cancelledRevenue[0].revenue).toFixed(2)}`
        }
      });
      return reportData;

    } catch (error) {
      logger.error('❌ Error fetching sales reports data:', error);
      throw error;
    }
  }

  // Perform subscription action
  async performSubscriptionAction(action, subscriptionId, adminUserId) {
    logger.info(`💳 Subscription action requested: ${action} for subscription ${subscriptionId}`);
    logger.warn('⚠️ Subscription actions not implemented yet');
    return { success: true, message: 'Action logged but not implemented' };
  }

  // Update user role
  async updateUserRole(userId, newRole, adminUserId) {
    logger.info(`👥 User role update requested: user ${userId} to role ${newRole}`);
    logger.warn('⚠️ User role updates not implemented yet');
    return { success: true, message: 'Role update logged but not implemented' };
  }

  // Get active subscription plans for admin
  async getActiveSubscriptionPlans() {
    logger.info('📋 Active subscription plans requested for admin');

    try {
      const db = getUserDb();
      const [plans] = await db.execute(`
        SELECT
          id,
          plan_type,
          name,
          description,
          price,
          currency,
          billing_cycle,
          credits,
          is_unlimited_credits,
          features,
          limits,
          is_active,
          sort_order,
          created_at,
          updated_at
        FROM subscription_plans
        WHERE is_active = 1 AND plan_type != 'EXPLORER'
        ORDER BY sort_order ASC, created_at DESC
      `);

      logger.info(`✅ Retrieved ${plans.length} active subscription plans for admin`);
      return plans;
    } catch (error) {
      logger.error('❌ Error fetching active subscription plans:', error);
      throw error;
    }
  }

  // Search user by email
  async searchUserByEmail(email) {
    logger.info(`🔍 Searching user by email: ${email}`);

    try {
      const db = getUserDb();

      // Get user basic info
      const [users] = await db.execute(`
        SELECT
          u.id as user_id,
          u.email,
          u.mobile,
          u.is_active as user_active,
          u.is_verified,
          u.created_at as user_created_at,
          up.first_name,
          up.last_name,
          up.profile_picture,
          up.plan as current_plan
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE u.email = ?
        LIMIT 1
      `, [email]);

      if (users.length === 0) {
        logger.info(`❌ No user found with email: ${email}`);
        return null;
      }

      const user = users[0];

      // Get all active subscriptions for the user
      const [subscriptions] = await db.execute(`
        SELECT
          us.id as subscription_id,
          us.status as subscription_status,
          us.start_date,
          us.end_date,
          sp.name as plan_name,
          sp.plan_type,
          'subscription' as item_type
        FROM user_subscriptions us
        INNER JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ? AND us.status = 'ACTIVE'
        ORDER BY us.created_at DESC
      `, [user.user_id]);

      // Get all active add-ons for the user
      const [addons] = await db.execute(`
        SELECT
          ua.id as addon_id,
          'ACTIVE' as addon_status,
          ua.created_at as start_date,
          NULL as end_date,
          sp.name as plan_name,
          sp.plan_type,
          ua.total_credits,
          ua.remaining_credits,
          ua.total_projects,
          ua.remaining_projects,
          ua.total_files_per_day,
          ua.remaining_files_per_day,
          'addon' as item_type
        FROM user_addons ua
        INNER JOIN subscription_plans sp ON ua.plan_id = sp.id
        WHERE ua.user_id = ? AND ua.is_active = 1
        ORDER BY ua.created_at DESC
      `, [user.user_id]);

      // Add subscription and addon info to user object
      user.active_subscriptions = subscriptions;
      user.active_addons = addons;
      user.all_active_items = [...subscriptions, ...addons];

      // For backward compatibility, add the first subscription as primary
      if (subscriptions.length > 0) {
        const primarySubscription = subscriptions[0];
        user.subscription_id = primarySubscription.subscription_id;
        user.subscription_status = primarySubscription.subscription_status;
        user.start_date = primarySubscription.start_date;
        user.end_date = primarySubscription.end_date;
        user.plan_name = primarySubscription.plan_name;
      }

      logger.info(`✅ User found: ${user.email} (ID: ${user.user_id}) with ${subscriptions.length} active subscriptions and ${addons.length} active add-ons`);
      return user;
    } catch (error) {
      logger.error('❌ Error searching user by email:', error);
      throw error;
    }
  }

  // Create admin subscription (bypassing payment)
  async createAdminSubscription(userId, planId, adminUserId) {
    logger.info(`➕ Creating admin subscription for user ${userId} with plan ${planId}`);

    try {
      const db = getUserDb();

      // Validate user exists
      const [users] = await db.execute(`
        SELECT id, email, is_active FROM users WHERE id = ?
      `, [userId]);

      if (users.length === 0) {
        throw new Error('User not found');
      }

      const user = users[0];
      if (!user.is_active) {
        throw new Error('User account is not active');
      }

      // Validate plan exists and is active
      const [plans] = await db.execute(`
        SELECT id, plan_type, name, price, billing_cycle, credits, is_unlimited_credits, is_active, limits
        FROM subscription_plans
        WHERE id = ? AND is_active = 1
      `, [planId]);

      if (plans.length === 0) {
        throw new Error('Plan not found or not active');
      }

      const plan = plans[0];

      // Handle ADDON plans differently from regular subscriptions
      if (plan.plan_type === 'ADDON') {
        return await this.createAdminAddon(userId, plan, adminUserId, db);
      }

      // Check if user already has an active subscription with the same plan type
      const [existingSubscriptions] = await db.execute(`
        SELECT us.id, us.status, us.plan_id, sp.plan_type
        FROM user_subscriptions us
        INNER JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ? AND us.status = 'ACTIVE'
      `, [userId]);

      // Check subscription logic based on plan type
      if (existingSubscriptions.length > 0) {
        // Check if user already has the same plan type
        const existingPlanTypes = existingSubscriptions.map(sub => sub.plan_type);

        if (existingPlanTypes.includes(plan.plan_type)) {
          throw new Error(`User already has an active ${plan.plan_type} subscription`);
        }

        // If adding ADDON, allow it regardless of current subscription
        if (plan.plan_type === 'ADDON') {
          logger.info(`✅ Adding ADDON subscription to user with existing subscriptions: ${existingPlanTypes.join(', ')}`);
        }
        // If adding different paid plan (CREATOR/PRO), cancel existing paid plans and create new (upgrade/downgrade)
        else if (plan.plan_type === 'CREATOR' || plan.plan_type === 'PRO') {
          // Find existing paid plans to cancel (not ADDON)
          const paidSubscriptionsToCancel = existingSubscriptions.filter(sub =>
            sub.plan_type === 'CREATOR' || sub.plan_type === 'PRO'
          );

          if (paidSubscriptionsToCancel.length > 0) {
            logger.info(`🔄 Upgrading/downgrading user from ${paidSubscriptionsToCancel.map(s => s.plan_type).join(', ')} to ${plan.plan_type}`);

            // Cancel existing paid subscriptions
            for (const subscription of paidSubscriptionsToCancel) {
              await db.execute(`
                UPDATE user_subscriptions
                SET status = 'CANCELLED',
                    cancelled_at = NOW(),
                    cancellation_reason = 'Admin upgrade/downgrade',
                    updated_at = NOW()
                WHERE id = ?
              `, [subscription.id]);

              logger.info(`✅ Cancelled existing ${subscription.plan_type} subscription`);
            }
          }
        }
      }

      // Generate UUID for subscription
      const { v4: uuidv4 } = require('uuid');
      const subscriptionId = uuidv4();

      // Calculate dates
      const startDate = new Date();
      let endDate = new Date();
      let nextBillingDate = new Date();

      // Set end date and next billing date based on billing cycle
      switch (plan.billing_cycle) {
        case 'WEEKLY':
          endDate.setDate(endDate.getDate() + 7);
          nextBillingDate.setDate(nextBillingDate.getDate() + 7);
          break;
        case 'MONTHLY':
          endDate.setMonth(endDate.getMonth() + 1);
          nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
          break;
        case 'YEARLY':
          endDate.setFullYear(endDate.getFullYear() + 1);
          nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
          break;
        case 'ONE_TIME':
          // For ADDON plans, set end date to null and no next billing
          endDate = null;
          nextBillingDate = null;
          break;
        default:
          endDate.setMonth(endDate.getMonth() + 1);
          nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
      }

      // Create subscription record
      await db.execute(`
        INSERT INTO user_subscriptions (
          id, user_id, plan_id, status, start_date, end_date, next_billing_date,
          razorpay_subscription_id, razorpay_customer_id, metadata, created_at, updated_at
        ) VALUES (?, ?, ?, 'ACTIVE', ?, ?, ?, NULL, NULL, ?, NOW(), NOW())
      `, [
        subscriptionId,
        userId,
        planId,
        startDate,
        endDate,
        nextBillingDate,
        JSON.stringify({
          created_by: 'admin',
          admin_user_id: adminUserId,
          created_via: 'admin_panel',
          bypass_payment: true
        })
      ]);

      // Update user profile plan (only for non-ADDON plans)
      if (plan.plan_type !== 'ADDON') {
        await db.execute(`
          UPDATE user_profiles SET plan = ?, updated_at = NOW() WHERE user_id = ?
        `, [plan.plan_type, userId]);
        logger.info(`✅ Updated user profile plan to ${plan.plan_type}`);
      }

      logger.info(`✅ Admin subscription created successfully for user ${userId}`);

      // Send subscription purchase confirmation email
      try {
        const user = await UserService.getUserById(userId);
        if (user && user.email) {
          await EmailService.sendSubscriptionPurchaseEmail(user.email, {
            user: {
              name: user.name,
              email: user.email,
            },
            plan: {
              name: plan.name,
              billing_cycle: plan.billing_cycle,
              credits: plan.credits,
              limits: plan.limits
            },
            transaction: {
              amount: plan.price || 0,
              currency: plan.currency || 'INR'
            },
            subscription: {
              next_billing_date: nextBillingDate
            }
          });
          logger.info(`📧 Subscription purchase email sent to ${user.email}`);
        }
      } catch (emailError) {
        logger.error('❌ Error sending subscription purchase email:', emailError);
        // Don't fail the subscription creation if email fails
      }

      return {
        subscriptionId,
        userId,
        planId,
        planName: plan.name,
        planType: plan.plan_type,
        status: 'ACTIVE',
        startDate,
        endDate,
        nextBillingDate
      };
    } catch (error) {
      logger.error('❌ Error creating admin subscription:', error);
      throw error;
    }
  }

  // Cancel admin subscription
  async cancelAdminSubscription(subscriptionId, reason, adminUserId) {
    logger.info(`❌ Cancelling admin subscription ${subscriptionId} with reason: ${reason}`);

    try {
      const db = getUserDb();

      // Validate subscription exists and is active
      const [subscriptions] = await db.execute(`
        SELECT
          us.id,
          us.user_id,
          us.plan_id,
          us.status,
          us.start_date,
          us.end_date,
          sp.name as plan_name,
          sp.plan_type,
          u.email as user_email
        FROM user_subscriptions us
        INNER JOIN subscription_plans sp ON us.plan_id = sp.id
        INNER JOIN users u ON us.user_id = u.id
        WHERE us.id = ? AND us.status = 'ACTIVE'
      `, [subscriptionId]);

      if (subscriptions.length === 0) {
        throw new Error('Active subscription not found');
      }

      const subscription = subscriptions[0];

      // Cancel the subscription
      await db.execute(`
        UPDATE user_subscriptions
        SET status = 'CANCELLED',
            cancelled_at = NOW(),
            cancellation_reason = ?,
            updated_at = NOW()
        WHERE id = ?
      `, [reason, subscriptionId]);

      // Downgrade user to free plan (EXPLORER)
      await db.execute(`
        UPDATE user_profiles
        SET plan = 'EXPLORER', updated_at = NOW()
        WHERE user_id = ?
      `, [subscription.user_id]);

      // Get free plan details for credit reset
      const [freePlans] = await db.execute(`
        SELECT credits FROM subscription_plans WHERE plan_type = 'EXPLORER' AND is_active = 1
      `);

      if (freePlans.length > 0) {
        const freeCredits = freePlans[0].credits;

        // Reset user credits to free plan limits
        await db.execute(`
          UPDATE user_credits
          SET credits = ?, updated_at = NOW()
          WHERE user_id = ?
        `, [freeCredits, subscription.user_id]);

        logger.info(`✅ User ${subscription.user_id} credits reset to ${freeCredits} (free plan)`);
      }

      logger.info(`✅ Subscription ${subscriptionId} cancelled successfully for user ${subscription.user_id}`);

      // Send subscription cancellation email
      try {
        const user = await UserService.getUserById(subscription.user_id);
        if (user && user.email) {
          await EmailService.sendSubscriptionCancelledEmail(user.email, {
            user: {
              name: user.name,
              email: user.email,
            },
            plan: {
              name: subscription.plan_name,
            },
            subscription: {
              cancelled_at: new Date(),
              cancellation_reason: reason,
              end_date: subscription.end_date
            }
          });
          logger.info(`📧 Subscription cancellation email sent to ${user.email}`);
        }
      } catch (emailError) {
        logger.error('❌ Error sending subscription cancellation email:', emailError);
        // Don't fail the cancellation if email fails
      }

      return {
        subscriptionId,
        userId: subscription.user_id,
        userEmail: subscription.user_email,
        planName: subscription.plan_name,
        planType: subscription.plan_type,
        status: 'CANCELLED',
        cancelledAt: new Date(),
        cancellationReason: reason,
        downgradedTo: 'EXPLORER'
      };
    } catch (error) {
      logger.error('❌ Error cancelling admin subscription:', error);
      throw error;
    }
  }

  // Cancel admin add-on
  async cancelAdminAddon(addonId, reason, adminUserId) {
    logger.info(`❌ Cancelling admin add-on ${addonId} with reason: ${reason}`);

    try {
      const db = getUserDb();

      // Validate add-on exists and is active
      const [addons] = await db.execute(`
        SELECT
          ua.id,
          ua.user_id,
          ua.plan_id,
          ua.is_active,
          ua.remaining_credits,
          ua.remaining_projects,
          ua.remaining_files_per_day,
          sp.name as plan_name,
          sp.plan_type,
          u.email as user_email
        FROM user_addons ua
        INNER JOIN subscription_plans sp ON ua.plan_id = sp.id
        INNER JOIN users u ON ua.user_id = u.id
        WHERE ua.id = ? AND ua.is_active = 1
      `, [addonId]);

      if (addons.length === 0) {
        throw new Error('Active add-on not found');
      }

      const addon = addons[0];

      // Deactivate the add-on
      await db.execute(`
        UPDATE user_addons
        SET is_active = 0, updated_at = NOW()
        WHERE id = ?
      `, [addonId]);

      // If add-on had remaining credits, deduct them from user account
      if (addon.remaining_credits > 0) {
        const { v4: uuidv4 } = require('uuid');

        // Deduct remaining credits from user account
        await db.execute(`
          UPDATE user_credits
          SET credits = GREATEST(0, credits - ?), updated_at = NOW()
          WHERE user_id = ?
        `, [addon.remaining_credits, addon.user_id]);

        // Create credit transaction record for deduction
        await db.execute(`
          INSERT INTO user_credit_transactions (
            id, user_id, type, amount, description, created_at
          ) VALUES (?, ?, 'DEBIT', ?, ?, NOW())
        `, [
          uuidv4(),
          addon.user_id,
          addon.remaining_credits,
          `Admin add-on cancellation: ${addon.plan_name} (${reason})`,
        ]);

        logger.info(`✅ Deducted ${addon.remaining_credits} remaining credits from user ${addon.user_id}`);
      }

      logger.info(`✅ Add-on ${addonId} cancelled successfully for user ${addon.user_id}`);

      return {
        addonId,
        userId: addon.user_id,
        userEmail: addon.user_email,
        planName: addon.plan_name,
        planType: addon.plan_type,
        status: 'CANCELLED',
        cancelledAt: new Date(),
        cancellationReason: reason,
        remainingCreditsDeducted: addon.remaining_credits
      };
    } catch (error) {
      logger.error('❌ Error cancelling admin add-on:', error);
      throw error;
    }
  }

  // Create admin add-on (separate from subscriptions)
  async createAdminAddon(userId, plan, adminUserId, db) {
    logger.info(`🎁 Creating admin add-on for user ${userId} with plan ${plan.name}`);

    try {
      // Generate UUID for addon and transaction
      const { v4: uuidv4 } = require('uuid');
      const addonId = uuidv4();
      const transactionId = uuidv4();

      // Parse plan limits
      let planLimits = {};
      try {
        planLimits = plan.limits ? JSON.parse(plan.limits) : {};
      } catch (error) {
        logger.warn('⚠️ Failed to parse plan limits, using defaults');
        planLimits = {};
      }

      const totalCredits = plan.credits || 0;
      const totalProjects = planLimits.projects || 0;
      const totalFilesPerDay = planLimits.filesPerDay || 0;

      // Create addon record in user_addons table
      await db.execute(`
        INSERT INTO user_addons (
          id, user_id, plan_id, transaction_id, total_credits, remaining_credits,
          total_projects, remaining_projects, total_files_per_day, remaining_files_per_day,
          is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
      `, [
        addonId,
        userId,
        plan.id,
        transactionId,
        totalCredits,
        totalCredits, // remaining_credits = total_credits initially
        totalProjects,
        totalProjects, // remaining_projects = total_projects initially
        totalFilesPerDay,
        totalFilesPerDay // remaining_files_per_day = total_files_per_day initially
      ]);

      // Add credits to user account if addon provides credits
      if (totalCredits > 0) {
        // Check if user_credits record exists
        const [existingCredits] = await db.execute(`
          SELECT id, credits FROM user_credits WHERE user_id = ?
        `, [userId]);

        if (existingCredits.length > 0) {
          // Update existing credits
          await db.execute(`
            UPDATE user_credits
            SET credits = credits + ?, updated_at = NOW()
            WHERE user_id = ?
          `, [totalCredits, userId]);
        } else {
          // Create new credits record
          await db.execute(`
            INSERT INTO user_credits (id, user_id, credits, created_at, updated_at)
            VALUES (?, ?, ?, NOW(), NOW())
          `, [uuidv4(), userId, totalCredits]);
        }

        // Create credit transaction record
        await db.execute(`
          INSERT INTO user_credit_transactions (
            id, user_id, type, amount, description, created_at
          ) VALUES (?, ?, 'CREDIT', ?, ?, NOW())
        `, [
          uuidv4(),
          userId,
          totalCredits,
          `Admin add-on pack: ${plan.name}`,
        ]);

        logger.info(`✅ Added ${totalCredits} credits to user ${userId} from add-on pack`);
      }

      logger.info(`✅ Admin add-on created successfully for user ${userId}`);

      return {
        addonId,
        userId,
        planId: plan.id,
        planName: plan.name,
        planType: plan.plan_type,
        status: 'ACTIVE',
        totalCredits,
        totalProjects,
        totalFilesPerDay,
        createdAt: new Date()
      };
    } catch (error) {
      logger.error('❌ Error creating admin add-on:', error);
      throw error;
    }
  }

  // Create a new role
  async createRole(name, description, permissions, adminUserId = 'admin') {
    // Format role name: uppercase and replace spaces with underscores
    const formattedName = name.trim().toUpperCase().replace(/\s+/g, '_');

    logger.info(`🔧 Creating new role: ${formattedName} (original: ${name})`);

    try {
      const db = getUserDb();
      const roleId = uuidv4();

      // Check if role name already exists
      const [existingRoles] = await db.execute(`
        SELECT id FROM roles WHERE name = ?
      `, [formattedName]);

      if (existingRoles.length > 0) {
        throw new Error(`Role with name '${formattedName}' already exists`);
      }

      // Insert new role
      await db.execute(`
        INSERT INTO roles (
          id, name, description, permissions, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, 1, NOW(), NOW())
      `, [
        roleId,
        formattedName,
        description,
        JSON.stringify(permissions || {})
      ]);

      logger.info(`✅ Role created successfully: ${formattedName} (ID: ${roleId})`);

      return {
        id: roleId,
        name: formattedName,
        description,
        permissions,
        is_active: true,
        created_at: new Date()
      };
    } catch (error) {
      logger.error('❌ Error creating role:', error);
      throw error;
    }
  }

  // Get all active roles
  async getAllRoles() {
    logger.info('📋 Getting all active roles');

    try {
      const db = getUserDb();

      const [roles] = await db.execute(`
        SELECT
          id,
          name,
          description,
          permissions,
          is_active,
          created_at,
          updated_at
        FROM roles
        WHERE is_active = 1
        ORDER BY created_at DESC
      `);

      logger.info(`✅ Retrieved ${roles.length} active roles (latest first)`);
      return roles;
    } catch (error) {
      logger.error('❌ Error fetching roles:', error);
      throw error;
    }
  }

  // Search user by email and get their roles
  async searchUserWithRoles(email) {
    logger.info(`🔍 Searching user with roles by email: ${email}`);

    try {
      const db = getUserDb();

      // Get user details
      const [users] = await db.execute(`
        SELECT
          u.id,
          u.email,
          u.mobile,
          u.is_active,
          u.is_verified,
          u.created_at,
          up.first_name,
          up.last_name,
          up.profile_picture,
          up.plan
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE u.email = ?
      `, [email]);

      if (users.length === 0) {
        return null;
      }

      const user = users[0];

      // Get user's current roles
      const [userRoles] = await db.execute(`
        SELECT
          r.id,
          r.name,
          r.description,
          ur.assigned_at,
          ur.assigned_by,
          ur.is_active
        FROM user_roles ur
        INNER JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = ? AND ur.is_active = 1
        ORDER BY r.name ASC
      `, [user.id]);

      logger.info(`✅ Found user ${email} with ${userRoles.length} roles`);

      return {
        ...user,
        roles: userRoles
      };
    } catch (error) {
      logger.error('❌ Error searching user with roles:', error);
      throw error;
    }
  }

  // Assign roles to a user
  async assignRolesToUser(userId, roleIds, adminUserId = 'admin') {
    logger.info(`👥 Assigning roles to user ${userId}: ${roleIds.join(', ')}`);

    try {
      const db = getUserDb();
      const connection = await db.getConnection();

      try {
        // Start transaction
        await connection.beginTransaction();

        // First, delete all existing role assignments for this user
        await connection.execute(`
          DELETE FROM user_roles WHERE user_id = ?
        `, [userId]);

        logger.info(`🗑️ Removed all existing role assignments for user ${userId}`);

        // Then assign new roles
        for (const roleId of roleIds) {
          const userRoleId = uuidv4();

          await connection.execute(`
            INSERT INTO user_roles (
              id, user_id, role_id, assigned_by, assigned_at, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, NOW(), 1, NOW(), NOW())
          `, [
            userRoleId,
            userId,
            roleId,
            adminUserId
          ]);
        }

        // Commit transaction
        await connection.commit();

        logger.info(`✅ Successfully assigned ${roleIds.length} roles to user ${userId}`);

        return {
          userId,
          assignedRoles: roleIds.length,
          assignedBy: adminUserId,
          assignedAt: new Date()
        };
      } catch (error) {
        // Rollback transaction on error
        await connection.rollback();
        throw error;
      } finally {
        // Release connection back to pool
        connection.release();
      }
    } catch (error) {
      logger.error('❌ Error assigning roles to user:', error);
      throw error;
    }
  }

  // Support Module Methods

  // Get open tickets count for sidebar
  async getOpenTicketsCount() {
    try {
      const db = getUserDb();

      const [openTicketsResult] = await db.execute(`
        SELECT COUNT(*) as count FROM user_tickets WHERE status = 'OPEN'
      `);

      return openTicketsResult[0].count;
    } catch (error) {
      logger.error('❌ Error fetching open tickets count:', error);
      return 0; // Return 0 if there's an error
    }
  }

  // Get support statistics
  async getSupportStats() {
    logger.info('🎫 Support statistics requested');

    try {
      const db = getUserDb();

      // Get total tickets count
      const [totalTicketsResult] = await db.execute(`
        SELECT COUNT(*) as count FROM user_tickets
      `);

      // Get open tickets count
      const [openTicketsResult] = await db.execute(`
        SELECT COUNT(*) as count FROM user_tickets WHERE status = 'OPEN'
      `);

      // Get closed tickets count (RESOLVED + CLOSED)
      const [closedTicketsResult] = await db.execute(`
        SELECT COUNT(*) as count FROM user_tickets WHERE status IN ('RESOLVED', 'CLOSED')
      `);

      // Get in progress tickets count
      const [inProgressTicketsResult] = await db.execute(`
        SELECT COUNT(*) as count FROM user_tickets WHERE status = 'IN_PROGRESS'
      `);

      // Get urgent tickets count
      const [urgentTicketsResult] = await db.execute(`
        SELECT COUNT(*) as count FROM user_tickets WHERE priority = 'URGENT' AND status NOT IN ('RESOLVED', 'CLOSED')
      `);

      const stats = {
        totalTickets: totalTicketsResult[0].count,
        openTickets: openTicketsResult[0].count,
        closedTickets: closedTicketsResult[0].count,
        inProgressTickets: inProgressTicketsResult[0].count,
        urgentTickets: urgentTicketsResult[0].count
      };

      logger.info(`✅ Retrieved support stats - Total: ${stats.totalTickets}, Open: ${stats.openTickets}, Closed: ${stats.closedTickets}`);
      return stats;
    } catch (error) {
      logger.error('❌ Error fetching support stats:', error);
      throw error;
    }
  }

  // Get all support tickets with pagination
  async getSupportTickets(options = {}) {
    const { page = 1, limit = 20, status, priority } = options;
    const offset = (page - 1) * limit;

    logger.info(`🎫 Support tickets requested - Page: ${page}, Limit: ${limit}, Status: ${status || 'all'}, Priority: ${priority || 'all'}`);

    try {
      const db = getUserDb();

      // Build WHERE clause and parameters
      let whereConditions = [];
      const params = [];

      if (status) {
        whereConditions.push('status = ?');
        params.push(status);
      }

      if (priority) {
        whereConditions.push('priority = ?');
        params.push(priority);
      }

      // Build WHERE clause for filtering
      let whereClause = '';
      if (status || priority) {
        const conditions = [];
        if (status) conditions.push(`ut.status = '${status}'`);
        if (priority) conditions.push(`ut.priority = '${priority}'`);
        whereClause = 'WHERE ' + conditions.join(' AND ');
      }

      // Get total count
      const [countResult] = await db.execute(`SELECT COUNT(*) as total FROM user_tickets ut ${whereClause}`);

      // Get tickets with user details
      const [tickets] = await db.execute(`
        SELECT
          ut.id,
          ut.user_id,
          ut.subject,
          ut.description,
          ut.priority,
          ut.status,
          ut.attachment_name,
          ut.attachment_type,
          ut.attachment_size,
          ut.attachment_secure_id,
          ut.tech_details,
          ut.created_at,
          ut.updated_at,
          u.email as user_email,
          u.mobile as user_mobile,
          up.first_name,
          up.last_name
        FROM user_tickets ut
        INNER JOIN users u ON ut.user_id = u.id
        LEFT JOIN user_profiles up ON u.id = up.user_id
        ${whereClause}
        ORDER BY ut.created_at DESC
        LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
      `);

      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);

      logger.info(`✅ Retrieved ${tickets.length} support tickets (${total} total)`);

      return {
        tickets,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: parseInt(limit),
          hasNextPage: parseInt(page) < totalPages,
          hasPreviousPage: parseInt(page) > 1
        }
      };
    } catch (error) {
      logger.error('❌ Error fetching support tickets:', error);
      throw error;
    }
  }

  // Get specific ticket by ID with user details
  async getSupportTicketById(ticketId) {
    logger.info(`🎫 Support ticket details requested for ID: ${ticketId}`);

    try {
      const db = getUserDb();

      const [tickets] = await db.execute(`
        SELECT
          ut.id,
          ut.user_id,
          ut.subject,
          ut.description,
          ut.priority,
          ut.status,
          ut.attachment_url,
          ut.attachment_name,
          ut.attachment_type,
          ut.attachment_size,
          ut.attachment_secure_id,
          ut.attachment_s3_url,
          ut.attachment_s3_key,
          ut.attachment_storage_type,
          ut.tech_details,
          ut.created_at,
          ut.updated_at,
          u.email as user_email,
          u.mobile as user_mobile,
          u.is_active as user_active,
          up.first_name,
          up.last_name,
          up.profile_picture
        FROM user_tickets ut
        INNER JOIN users u ON ut.user_id = u.id
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE ut.id = ?
      `, [ticketId]);

      if (tickets.length === 0) {
        logger.warn(`❌ Support ticket not found: ${ticketId}`);
        return null;
      }

      const ticket = tickets[0];
      logger.info(`✅ Retrieved support ticket: ${ticket.subject}`);

      return ticket;
    } catch (error) {
      logger.error('❌ Error fetching support ticket:', error);
      throw error;
    }
  }

  // Update ticket status
  async updateTicketStatus(ticketId, newStatus, adminUserId = 'admin') {
    logger.info(`🎫 Updating ticket ${ticketId} status to ${newStatus}`);

    try {
      const db = getUserDb();

      // First get the ticket details for email notification
      const ticket = await this.getSupportTicketById(ticketId);
      if (!ticket) {
        throw new Error('Ticket not found');
      }

      // Update the ticket status
      await db.execute(`
        UPDATE user_tickets
        SET status = ?, updated_at = NOW()
        WHERE id = ?
      `, [newStatus, ticketId]);

      logger.info(`✅ Ticket ${ticketId} status updated to ${newStatus}`);

      // Send status update email to user
      await this.sendTicketStatusUpdateEmail(ticket, newStatus);

      return {
        ticketId,
        oldStatus: ticket.status,
        newStatus,
        updatedAt: new Date()
      };
    } catch (error) {
      logger.error('❌ Error updating ticket status:', error);
      throw error;
    }
  }

  // Add admin reply to ticket (this would require a ticket_replies table in a full implementation)
  // For now, we'll simulate this by updating the ticket with admin notes
  async addTicketReply(ticketId, replyMessage, adminUserId = 'admin') {
    logger.info(`🎫 Adding admin reply to ticket ${ticketId}`);

    try {
      const db = getUserDb();

      // Get the ticket details for email notification
      const ticket = await this.getSupportTicketById(ticketId);
      if (!ticket) {
        throw new Error('Ticket not found');
      }

      // In a full implementation, you would insert into a ticket_replies table
      // For now, we'll update the ticket status to IN_PROGRESS if it's OPEN
      if (ticket.status === 'OPEN') {
        await db.execute(`
          UPDATE user_tickets
          SET status = 'IN_PROGRESS', updated_at = NOW()
          WHERE id = ?
        `, [ticketId]);
      }

      logger.info(`✅ Admin reply added to ticket ${ticketId}`);

      // Send reply email to user
      await this.sendTicketReplyEmail(ticket, replyMessage);

      return {
        ticketId,
        replyMessage,
        adminUserId,
        repliedAt: new Date()
      };
    } catch (error) {
      logger.error('❌ Error adding ticket reply:', error);
      throw error;
    }
  }

  // Send ticket status update email
  async sendTicketStatusUpdateEmail(ticket, newStatus) {
    try {
      const userEmail = ticket.user_email;
      if (!userEmail) {
        logger.warn(`No email found for ticket ${ticket.id}, skipping status update email`);
        return;
      }

      const userName = ticket.first_name && ticket.last_name
        ? `${ticket.first_name} ${ticket.last_name}`.trim()
        : 'Valued Customer';

      const statusDisplayMap = {
        'OPEN': 'Open',
        'IN_PROGRESS': 'In Progress',
        'RESOLVED': 'Resolved',
        'CLOSED': 'Closed'
      };

      const emailData = {
        userName,
        ticket: {
          ticketId: ticket.id,
          subject: ticket.subject,
          status: newStatus,
          statusDisplay: statusDisplayMap[newStatus] || newStatus,
          updatedAt: new Date().toLocaleString()
        }
      };

      await EmailService.sendEmail(
        userEmail,
        `Ticket Status Update - #${ticket.id}`,
        'ticket-status-update',
        emailData
      );

      logger.info(`✅ Status update email sent to ${userEmail} for ticket ${ticket.id}`);
    } catch (error) {
      logger.error('❌ Error sending status update email:', error);
      // Don't throw error - email failure shouldn't break the status update
    }
  }

  // Send ticket reply email
  async sendTicketReplyEmail(ticket, replyMessage) {
    try {
      const userEmail = ticket.user_email;
      if (!userEmail) {
        logger.warn(`No email found for ticket ${ticket.id}, skipping reply email`);
        return;
      }

      const userName = ticket.first_name && ticket.last_name
        ? `${ticket.first_name} ${ticket.last_name}`.trim()
        : 'Valued Customer';

      const emailData = {
        userName,
        ticket: {
          ticketId: ticket.id,
          subject: ticket.subject,
          replyMessage,
          repliedAt: new Date().toLocaleString()
        }
      };

      await EmailService.sendEmail(
        userEmail,
        `New Reply to Your Support Ticket - #${ticket.id}`,
        'ticket-reply',
        emailData
      );

      logger.info(`✅ Reply email sent to ${userEmail} for ticket ${ticket.id}`);
    } catch (error) {
      logger.error('❌ Error sending reply email:', error);
      // Don't throw error - email failure shouldn't break the reply
    }
  }
}

module.exports = new AdminService();
