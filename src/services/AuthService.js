const bcrypt = require('bcryptjs');
const { logger } = require('../config/logger');
const { getUserDb } = require('../config/db');
const { JWTUtil } = require('../middleware/auth');

/**
 * Authentication Service Class
 * Handles user authentication using the same database and logic as main app
 */
class AuthService {
  /**
   * Login with email/mobile and password
   * @param {Object} loginData - Login credentials
   * @param {string} loginData.identifier - Email or mobile
   * @param {string} loginData.password - Password
   * @returns {Promise<Object>} Token and user data
   */
  static async loginWithPassword(loginData) {
    try {
      const { identifier, password } = loginData;

      if (!identifier || !password) {
        throw new Error('Email/mobile and password are required');
      }

      logger.info(`🔐 Login attempt for: ${identifier}`);

      // Find user by email or mobile using raw query
      const db = getUserDb();
      const [users] = await db.execute(`
        SELECT
          u.id,
          u.email,
          u.mobile,
          u.password,
          u.is_active,
          u.is_verified,
          u.created_at,
          up.first_name,
          up.last_name,
          up.profile_picture
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE (u.email = ? OR u.mobile = ?) AND u.is_active = 1 AND u.is_verified = 1
      `, [identifier, identifier]);

      if (users.length === 0) {
        logger.warn(`Login failed: User not found or inactive: ${identifier}`);
        throw new Error('Invalid credentials or account not verified');
      }

      const user = users[0];

      // Validate password
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        logger.warn(`Login failed: Invalid password for user: ${identifier}`);
        throw new Error('Invalid credentials');
      }

      // Generate JWT token with same payload structure as main app
      const tokenPayload = {
        userId: user.id,
        email: user.email,
        mobile: user.mobile,
      };

      const token = JWTUtil.generateToken(tokenPayload);

      logger.info(`✅ User logged in successfully: ${user.id} (${user.email})`);

      return {
        token,
        user: {
          id: user.id,
          email: user.email,
          mobile: user.mobile,
          firstName: user.first_name,
          lastName: user.last_name,
          profilePicture: user.profile_picture,
          isActive: user.is_active,
          isVerified: user.is_verified,
          createdAt: user.created_at
        },
      };
    } catch (error) {
      logger.error('❌ Error in password login:', error);
      throw error;
    }
  }

  /**
   * Verify JWT token and get user data
   * @param {string} token - JWT token
   * @returns {Promise<Object>} Token payload and user data
   */
  static async verifyToken(token) {
    try {
      const payload = JWTUtil.verifyToken(token);

      // Verify user still exists, is active, and is verified
      const db = getUserDb();
      const [users] = await db.execute(`
        SELECT 
          u.id,
          u.email,
          u.mobile,
          u.is_active,
          u.is_verified,
          up.first_name,
          up.last_name,
          up.profile_picture
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE u.id = ? AND u.is_active = 1 AND u.is_verified = 1
      `, [payload.userId]);

      if (users.length === 0) {
        logger.warn(`Token verification failed: User not found or inactive: ${payload.userId}`);
        throw new Error('User not found or inactive');
      }

      const user = users[0];

      logger.debug(`✅ Token verified for user: ${user.id} (${user.email})`);

      return {
        ...payload,
        user: {
          id: user.id,
          email: user.email,
          mobile: user.mobile,
          firstName: user.first_name,
          lastName: user.last_name,
          profilePicture: user.profile_picture,
          isActive: user.is_active,
          isVerified: user.is_verified
        }
      };
    } catch (error) {
      logger.error('❌ Error verifying token:', error);
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Check if user exists by email or mobile
   * @param {string} identifier - Email or mobile
   * @returns {Promise<Object|null>} User data or null if not found
   */
  static async checkUserExists(identifier) {
    try {
      if (!identifier) {
        throw new Error('Email or mobile is required');
      }

      const db = getUserDb();
      const [users] = await db.execute(`
        SELECT 
          u.id,
          u.email,
          u.mobile,
          u.is_active,
          u.is_verified,
          up.first_name,
          up.last_name
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE (u.email = ? OR u.mobile = ?)
      `, [identifier, identifier]);

      if (users.length === 0) {
        return null;
      }

      const user = users[0];
      logger.debug(`User found: ${user.id} (${user.email})`);

      return {
        id: user.id,
        email: user.email,
        mobile: user.mobile,
        firstName: user.first_name,
        lastName: user.last_name,
        isActive: user.is_active,
        isVerified: user.is_verified
      };
    } catch (error) {
      logger.error('❌ Error checking user existence:', error);
      throw error;
    }
  }

  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean} True if valid email format
   */
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate mobile format (Indian mobile numbers)
   * @param {string} mobile - Mobile to validate
   * @returns {boolean} True if valid mobile format
   */
  static isValidMobile(mobile) {
    const mobileRegex = /^[6-9]\d{9}$/;
    return mobileRegex.test(mobile);
  }

  /**
   * Validate identifier (email or mobile)
   * @param {string} identifier - Email or mobile to validate
   * @returns {Object} Validation result with type
   */
  static validateIdentifier(identifier) {
    if (!identifier || !identifier.trim()) {
      return { isValid: false, type: null, message: 'Email or mobile is required' };
    }

    const trimmedIdentifier = identifier.trim();

    if (this.isValidEmail(trimmedIdentifier)) {
      return { isValid: true, type: 'email', identifier: trimmedIdentifier };
    }

    if (this.isValidMobile(trimmedIdentifier)) {
      return { isValid: true, type: 'mobile', identifier: trimmedIdentifier };
    }

    return { isValid: false, type: null, message: 'Invalid email or mobile format' };
  }

  /**
   * Get user profile by user ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User profile data
   */
  static async getUserProfile(userId) {
    try {
      const db = getUserDb();
      const [users] = await db.execute(`
        SELECT 
          u.id,
          u.email,
          u.mobile,
          u.is_active,
          u.is_verified,
          u.created_at,
          up.first_name,
          up.last_name,
          up.profile_picture,
          up.plan
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE u.id = ?
      `, [userId]);

      if (users.length === 0) {
        throw new Error('User not found');
      }

      const user = users[0];
      logger.debug(`User profile retrieved: ${user.id} (${user.email})`);

      return {
        id: user.id,
        email: user.email,
        mobile: user.mobile,
        firstName: user.first_name,
        lastName: user.last_name,
        profilePicture: user.profile_picture,
        plan: user.plan,
        isActive: user.is_active,
        isVerified: user.is_verified,
        createdAt: user.created_at
      };
    } catch (error) {
      logger.error('❌ Error getting user profile:', error);
      throw error;
    }
  }

  /**
   * Refresh JWT token
   * @param {string} token - Current JWT token
   * @returns {Promise<Object>} New token
   */
  static async refreshToken(token) {
    try {
      const payload = await this.verifyToken(token);

      // Generate new token with same payload structure
      const newTokenPayload = {
        userId: payload.userId,
        email: payload.email,
        mobile: payload.mobile,
      };

      const newToken = JWTUtil.generateToken(newTokenPayload);

      logger.info(`🔄 Token refreshed for user: ${payload.userId}`);

      return {
        token: newToken,
        user: payload.user
      };
    } catch (error) {
      logger.error('❌ Error refreshing token:', error);
      throw error;
    }
  }
}

module.exports = { AuthService };
