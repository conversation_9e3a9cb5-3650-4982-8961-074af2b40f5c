# Super Admin Setup Guide

This guide explains how to set up a SUPER_ADMIN role for a user in the production environment.

## Prerequisites

1. Access to the MySQL database
2. User email address that needs SUPER_ADMIN access

## Step 1: Create SUPER_ADMIN Role (if not exists)

```sql
-- Check if SUPER_ADMIN role exists
SELECT * FROM roles WHERE name = 'SUPER_ADMIN';

-- If it doesn't exist, create it
INSERT INTO roles (
    id, 
    name, 
    description, 
    permissions, 
    is_active, 
    created_at, 
    updated_at
) VALUES (
    UUID(), 
    'SUPER_ADMIN', 
    'Super Administrator with full access to admin dashboard', 
    '{}', 
    1, 
    NOW(), 
    NOW()
);
```

## Step 2: Find User ID

```sql
-- Find the user by email
SELECT id, email, first_name, last_name 
FROM users 
WHERE email = '<EMAIL>';
```

## Step 3: Assign SUPER_ADMIN Role to User

```sql
-- Get the role ID for SUPER_ADMIN
SET @role_id = (SELECT id FROM roles WHERE name = 'SUPER_ADMIN' LIMIT 1);

-- Get the user ID (replace with actual email)
SET @user_id = (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1);

-- Remove any existing role assignments for this user (optional)
DELETE FROM user_roles WHERE user_id = @user_id;

-- Assign SUPER_ADMIN role to the user
INSERT INTO user_roles (
    id,
    user_id,
    role_id,
    assigned_by,
    assigned_at,
    is_active,
    created_at,
    updated_at
) VALUES (
    UUID(),
    @user_id,
    @role_id,
    'system',
    NOW(),
    1,
    NOW(),
    NOW()
);
```

## Step 4: Verify Assignment

```sql
-- Verify the role assignment
SELECT 
    u.email,
    u.first_name,
    u.last_name,
    r.name as role_name,
    ur.assigned_at
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>'
AND ur.is_active = 1;
```

## Complete Script Example

Replace `<EMAIL>` with the actual email address:

```sql
-- Complete setup script
START TRANSACTION;

-- Create SUPER_ADMIN role if it doesn't exist
INSERT IGNORE INTO roles (
    id, 
    name, 
    description, 
    permissions, 
    is_active, 
    created_at, 
    updated_at
) VALUES (
    UUID(), 
    'SUPER_ADMIN', 
    'Super Administrator with full access to admin dashboard', 
    '{}', 
    1, 
    NOW(), 
    NOW()
);

-- Set variables
SET @role_id = (SELECT id FROM roles WHERE name = 'SUPER_ADMIN' LIMIT 1);
SET @user_id = (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1);

-- Check if user exists
SELECT @user_id as user_found;

-- Assign role (only if user exists)
INSERT INTO user_roles (
    id,
    user_id,
    role_id,
    assigned_by,
    assigned_at,
    is_active,
    created_at,
    updated_at
) 
SELECT 
    UUID(),
    @user_id,
    @role_id,
    'system',
    NOW(),
    1,
    NOW(),
    NOW()
WHERE @user_id IS NOT NULL;

-- Verify
SELECT 
    u.email,
    r.name as role_name,
    'SUCCESS' as status
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.id = @user_id
AND ur.is_active = 1;

COMMIT;
```

## Important Notes

1. **Replace the email**: Make sure to replace `<EMAIL>` with the actual email address
2. **User must exist**: The user must already be registered in the system
3. **Database backup**: Always backup the database before running these queries in production
4. **Test first**: Test these queries in a development environment first

## Troubleshooting

### User not found
If the user is not found, make sure:
- The email address is correct
- The user has registered and verified their account
- Check the `users` table directly

### Role assignment failed
If role assignment fails:
- Check if the role was created successfully
- Verify the user ID is not NULL
- Check for any foreign key constraints

### Access still denied
If the user still can't access the admin dashboard:
- Clear browser cache and cookies
- Log out and log back in
- Check server logs for any errors
- Verify the role name is exactly 'SUPER_ADMIN' (case sensitive)
