{"name": "theinfini_ai_internals", "version": "1.0.0", "description": "Admin dashboard for theinfini AI internals management", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["admin", "dashboard", "<PERSON><PERSON><PERSON><PERSON>", "ai", "internals"], "author": "TheInfini AI Team", "license": "ISC", "dependencies": {"aws-sdk": "^2.1692.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-session": "^1.18.2", "handlebars": "^4.7.8", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.14.2", "nodemailer": "^7.0.5", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}